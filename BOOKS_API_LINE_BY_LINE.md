### books-api.ts deep dive and line-by-line walkthrough

This document provides a detailed walkthrough of `electron/main/api/books-api.ts`, explaining each function, the rationale behind its implementation, and notable nuances. Line references (e.g., L100) correspond to the source file lines at the time of this analysis.

Conventions used here:
- Line ranges are noted as Lx–Ly. When helpful, short code fragments are quoted inline.
- This module is a collection of utility functions and exported APIs; it is not a class.

---

## Imports and setup (L1–L33)

- L1: A high-level comment describing the file’s purpose: specialized book operations, including OpenLibrary integration.
- L2–L19: Imports from `../database/database-api` expose CRUD for books and notes, query helpers, and folder deletion. These enable the higher-level behavior in this module:
  - `createBook`, `getAllBooks`, `getAllBooksWithNoteCounts`, `getBookById`, `getBookByIsbn`, `getBookByOlid`, `updateBook`, and `deleteBook` (aliased as `deleteBookFromDb`).
  - `dbAll`: a helper for SQL queries that return multiple rows.
  - `getNotesByFolderId`, `updateNote`: used when deleting books and un-linking notes.
  - `deleteFolderFromDb`: used to delete empty folders after book removal.
- L20: Import media helpers: `saveBookCover`, `getBookCover`, `filePathToMediaUrl`.
- L21–L26: Import folder utilities: `ensureBooksRootFolder`, `getBookFolder`, `updateFolderWithValidation`, `createFolderWithValidation`. These ensure folder operations emit correct backup/sync events and validation.
- L27: `axios` for HTTP/JSON OpenLibrary APIs.
- L28–L29: Node `http`/`https` are used for low-level streaming for image downloads (reasons: redirects, custom headers, long timeouts, and fine-grained error handling).
- L30–L31: Utility imports: `convertLanguageCode` (map OL language keys to readable names), `sanitizeBookTitle` (for folder names).
- L32: `notifyBookChange` for emitting database change hook events (create/update/delete) used elsewhere in the app.

---

## OpenLibrary data contracts (L34–L97)

- L34–L51 `OpenLibrarySearchResult`: An interface mirroring fields returned by `search.json`. Key fields include `title`, `author_name`, `isbn`, `cover_i`, `cover_edition_key`, `key` (e.g., `/works/OL...W`).
- L53–L71 `OpenLibraryWorkDetails`: Shape of a Work response (`/works/{olid}.json`), including nested authors, description, subjects, first publish date, and `covers` numeric ids.
- L73–L76 `OpenLibraryAuthorDetails`: Minimal shape of an author response.
- L78–L97 `OpenLibraryEditionDetails`: Shape of an Edition response (`/works/{olid}/editions.json` entries), including isbn arrays, pages, publishers, languages, covers, description, and key.

---

## Extended search result type (L99–L110)

- L99–L110 `BookSearchResult` extends `OpenLibrarySearchResult` with UI/logic-centric fields:
  - `cover_url`: resolved URL string (e.g., from `cover_i` or edition data).
  - `description`, `genres`, `isbn_primary` (picked primary), `relevanceScore` for custom ranking, and several cover override flags (`hasCustomCover`, `userModifiedCover`, `preventCoverOverride`).

---

## Search cache and helpers (L111–L120)

- L111–L115: `searchCache` and `cacheTimestamps` store memoized results per `(query, limit, localBooks.length)` with a 5-minute expiry (`CACHE_EXPIRY_TIME`). This avoids redundant calls and stabilizes UX.

---

## Cover URL helper (L116–L120)

- L116–L119 `getOpenLibraryCoverUrl(coverKey, size)`: Constructs a URL to OpenLibrary covers service `https://covers.openlibrary.org/b/id/{id}-{size}.jpg`. Used consistently to resolve cover images for display/download.

---

## Robust image download (L121–L188)

- L121–L188 `downloadCoverImageData(coverUrl)`: Downloads image as a raw `Buffer`. Notable design choices:
  - L123–L180 `downloadWithRedirects(url, redirectCount)`: Internal helper implementing manual follow-redirects logic up to 5 redirects (L124–L148). Ensures headers (`User-Agent`, broad `Accept`) and a generous timeout (30s) per request (L134–L138). Uses Node `http`/`https` (L131) for stream control.
  - Redirect logic: 301/302/303/307/308 handled uniformly by recursively calling itself with incremented `redirectCount` (L141–L148).
  - Non-200: reject with specific HTTP code/message (L152–L155) to surface issues like 404, 500.
  - Stream assembly: accumulate chunks (L157–L159), concat at `end` (L160–L166), error/timeout handlers (L167, L170–L178).
  - Outer `try/catch` (L182–L188): returns the buffer or throws a well-defined error for callers.

Nuance: Using low-level HTTP ensures predictable behavior for binary content, avoids axios transformations, and provides better control over errors and timeouts.

---

## ISBN detection (L190–L206)

- `isISBN(query)`: Strips hyphens/spaces (L196–L197) and applies regex for ISBN-10 (L199–L201) and ISBN-13 (L202–L203). Returns boolean (L205). This drives an alternate fast path using OL’s Books API by bibkey instead of general `search.json`.

---

## String similarity primitives (L208–L285)

- L214–L235 `getEditDistance(str1, str2)`: Space-optimized Levenshtein implementation using a single array `costs`. It iterates `str2` outer, `str1` inner, updating costs for insert/replace/delete (L223–L226) and carrying forward `lastValue` (L227–L233).
- L244–L285 `getEnhancedStringSimilarity(str1, str2)`: Composite similarity:
  - Short-circuit exact (1.0) (L250–L251).
  - Substring bonus (L253–L257): If longer contains shorter, returns 0.85 + proportional term.
  - Otherwise compute `basicSimilarity` from edit distance (L258–L261).
  - Position bonuses for common prefix (L266–L273) and suffix (L275–L282), with smaller weight for suffix.
  - Clamp to 1.0 (L284–L285).

Nuance: Prefers titles that start with the query and tolerates partial/near matches for improved UX on book title fuzzy search.

---

## Word-level fuzzy match (L287–L330)

- `isAdvancedWordSimilar(word1, word2)`: Optimized for book titles/authors:
  - Short words (≤2) must match exactly (L294–L296) to avoid noise.
  - Early fast-paths: equality or substring (L299–L301).
  - Supports common abbreviations (e.g., dr→doctor, &→and) via `abbreviationMap` (L302–L307, L312–L315).
  - Dynamic thresholds (L316–L327) based on length and length disparity.
  - Uses `getEnhancedStringSimilarity` (L328–L329) with the chosen threshold.

---

## Query preprocessing (L332–L358)

- `preprocessQuery(query)`: Generates variations to absorb typos (e.g., teh→the) using a small `typoMap` (L341–L349). Returns original and corrected variant if changed (L351–L357). Used to improve downstream relevance scoring and search quality.

---

## Relevance scoring (L360–L525)

- `calculateEnhancedRelevanceScore(book, queries, isISBNQuery, localBooks)` computes a composite relevance score used to sort search results:
  - Exact ISBN match returns 10000 immediately (L379–L385) after normalizing query ISBN (L381–L382).
  - Penalize duplicates already present locally by ISBN or OLID (L387–L395), preventing re-adding the same work.
  - For each query variation (L397–L482):
    - Title-level matches: exact (+5000), startswith (+2500), includes (+1200), or fuzzy via `getEnhancedStringSimilarity` with tiered points (L401–L416).
    - Word-by-word analysis (L418–L464): counts exact matches (+150 each), fuzzy matches (scaled, L439–L444), and author matches (exact +100 or fuzzy with threshold, L446–L462).
    - Multi-word bonuses (L466–L481): adds for higher exact/total match ratios and near-complete matches.
  - Cover prioritization (L484–L492): +25 if any cover reference, +15 extra if `cover_i` (higher-quality OL image).
  - Metadata-derived boosts (L494–L522): recency bias using `first_publish_year`, bonuses for metadata richness (authors/isbn/publisher/subject/language) and a capped preference for multiple editions.
  - Ensures non-negative result (L524–L525).

Nuance: The scoring intentionally mixes lexical, fuzzy, author-aware, and metadata/UX signals to produce user-pleasing top results, and it downranks already-owned items.

---

## ISBN-targeted search (L527–L565)

- `searchBooksByISBN(isbn)`: Uses OL Books API `api/books?bibkeys=ISBN:{isbn}&format=json&jscmd=data` (L533–L535) with a longer timeout (25s) (L537–L541). It maps the bibkey entry into a `BookSearchResult` (L547–L561), including:
  - Authors flattened (L551), cover derived by parsing `cover.medium` path for the numeric id (L552), publish date extracted to year (L553), identifiers normalized to `isbn` array (L554).
  - Language codes converted to names via `convertLanguageCode` from OL language keys (L556).
  - `isbn_primary` set to the incoming argument (L558) and `relevanceScore` preset to 10000 (L559).

---

## Online search orchestrator (L567–L678)

- `searchBooksOnline(query, limit=10, localBooks=[])`:
  - Caps limit at 10 (L574–L576). Short queries (<2 chars) are ignored to avoid noisy calls (L577–L581).
  - Cache lookup keyed by `query_limit_localCount` (L583–L595) with 5-minute expiry.
  - If `isISBN`, delegates to `searchBooksByISBN` (L599–L606) and still runs `calculateEnhancedRelevanceScore` for consistency and dedupe interaction.
  - Otherwise, preprocess query (L608–L611), call `search.json` with fields constrained for perf (L612–L615, L616–L621).
  - Map docs into `BookSearchResult` (L623–L643):
    - Compute `olid` from `key` (L625–L629).
    - `cover_url` via `cover_i` (L630–L631).
    - `genres`: derive a single, display-friendly tag from the first subject (first word, capitalized) (L631–L637).
    - `isbn_primary`: the first available isbn for convenience (L638), and `relevanceScore` via the enhanced scorer (L639).
  - Sort by `relevanceScore`, filter out non-positive, and slice to limit (L645–L650).
  - Writes to cache and trims oldest entries when size >100 (L652–L666).
  - Logs counts, including how many have covers (L668). On errors, returns empty array to degrade gracefully (L671–L677).

Nuance: Strong emphasis on UX: faster return for short queries (skip), robust timeouts, limited fields, and meaningful ranking.

---

## Detailed book fetch (L680–L857)

- `getBookDetailsFromOpenLibrary(olid)`:
  - Fetch work JSON (L684–L691). Initialize defaults (L693–L701).
  - Author names: For each `work.authors`, try fetching `/authors/{key}.json` with retry up to 2 attempts and longer timeouts (10s) (L703–L726). If all fail, fall back to deriving placeholders from author keys (L738–L746).
  - Description: Supports both string and object shapes (L749–L756).
  - Genres: Pick first subject and use first word capitalized (L758–L768) to keep display terse.
  - Editions: Fetch `/works/{olid}/editions.json` (L779–L785). Prefer an edition with ISBN-13/10 (L789–L793), otherwise fall back to first.
    - Extract `isbn` with preference for ISBN-13 (L795–L800).
    - `page_count` (L802–L805), `publication_date` normalized via `extractPublicationYear` (L807–L813), `language` via `convertLanguageCode` (L814–L820).
    - Cover URL via edition `covers` or work `covers` fallback (L822–L827).
  - If editions fetch fails, fall back to work-level date/cover (L830–L838).
  - Return a `Partial<Book>` normalized for downstream create flows (L840–L851).
  - Errors throw a standardized message (L853–L856).

Nuance: Multi-stage enrichment to surface better metadata when available, yet resilient with progressive fallbacks and retries.

---

## Data validation (L859–L903)

- `validateAndTransformBookData(bookData)`:
  - Ensures `title` is a non-empty string (L861–L864).
  - Constructs a `Book` object filling unspecified optional fields with `null` or defaults (e.g., `status: 'unread'`) (L866–L883).
  - Validates value ranges: `rating` in [0,5] (L886–L888), `page_count` and `current_page` non-negative (L890–L896), and `current_page <= page_count` (L898–L900).
  - Returns normalized `Book` (L902–L903).

Nuance: Centralizing validation ensures consistent constraints before writes and simplifies callers.

---

## Timeout detection (L905–L910)

- `isTimeoutError(error)`: Heuristics for axios/Node network timeouts by inspecting `code` and `message` (ECONNABORTED, ETIMEDOUT, contains 'timeout'). Used in retry logic and error messaging.

---

## Publication year extraction (L912–L962)

- `extractPublicationYear(dateString)` handles diverse formats:
  - BCE/BC forms like "32 BCE", "32 BC" → returns `-32` (L924–L929).
  - Already negative years pass through (L931–L935).
  - Modern years: First 4-digit match (L937–L949). Rejects future years (L943–L946).
  - Fallback: `new Date(dateString)` parse with `getFullYear` if valid (L951–L956).
  - Returns `null` if nothing parsable (L960–L961).

Nuance: Keeps storage consistent (year-only), while being robust to OpenLibrary’s varied date strings.

---

## Data URL helpers (L964–L973)

- `isDataUrl(url)`: Simple prefix check for `data:image/` (L965–L967).
- `dataUrlToBuffer(dataUrl)`: Splits at comma and decodes base64 payload (L971–L973). Used to persist user-provided covers into file-backed media and later clean database fields.

---

## Repair: download missing covers (L975–L1042)

- `checkAndDownloadMissingCovers()`:
  - SQL query (L981–L988) finds books with a non-empty `cover_url` but no corresponding `media_files` row marked `is_cover = 1`.
  - Early exit if none (L992–L995). Otherwise, processes in small batches of 3 (L1002–L1005) to avoid resource spikes/rate-limits.
  - For each book:
    - If `cover_url` is a data URL, decode and save via `saveBookCover(book.id, buffer, 'cover.jpg')` (L1011–L1016).
    - Else, download via `downloadCoverImageData` and save (L1017–L1021).
  - Uses `Promise.allSettled` per-batch (L1030) and inter-batch delay 1s (L1032–L1035).
  - Logs a concise summary (L1038) and swallows unexpected errors (L1039–L1041) to avoid affecting the app flow.

Nuance: Bridges the gap between earlier design (base64 URLs stored in `books.cover_url`) and the optimized media file storage by backfilling missing files.

---

## Create a book with cover handling and folder creation (L1044–L1136)

- `createBookWithValidation(bookData, downloadCover = true)`:
  - Validates and normalizes incoming data (L1046–L1047).
  - If `downloadCover` and `cover_url` is an external URL (not a data URL), downloads it immediately, converts to base64, and sets it back on the data (L1049–L1060). If it’s already a data URL, skip download (L1061–L1063).
  - Creates the book row (L1067).
  - Emits a `notifyBookChange('create', ...)` event (L1069–L1076) for downstream systems (sync, UI, hooks).
  - Critical fix (L1078–L1094): If the stored `cover_url` is base64, immediately convert to a file via `saveBookCover` and then clear the base64 from the DB using `updateBook(..., { cover_url: null })`. This avoids manifest/database bloat and ensures the cover is in `media_files`.
  - Book folder creation (L1096–L1134):
    - Fetch or create the `Books` root (L1102–L1106).
    - Use `sanitizeBookTitle` for Unicode-safe folder names (L1111–L1115).
    - Create a folder linked to the book via `book_id` using `createFolderWithValidation` (L1121–L1125).
    - Non-fatal errors are logged; book creation continues (L1129–L1133).
  - Returns the created book (L1135–L1136).

Nuance: This function ensures the UX-critical cover is persisted and the filesystem/folder structure is consistent right after creation, while optimizing DB size by removing base64 blobs.

---

## Update a book and emit events (L1138–L1161)

- `updateBookWithValidation(id, bookUpdates)`:
  - Validates `id` type (L1141–L1143), then delegates to `updateBook` (L1145–L1147).
  - Emits `notifyBookChange('update', ...)` with `updatedFields` for downstream systems (L1148–L1154).
  - Returns updated row or throws a standardized error (L1156–L1160).

---

## Delete book with folder/notes handling (L1163–L1245)

- `deleteBookAndHandleFolder(bookId)` orchestrates a complex delete workflow:
  - Validates `bookId` (L1164–L1166). Fetches the book’s folder (L1171–L1175).
  - If the folder exists:
    - Query notes inside it (L1176–L1177).
    - If there are notes:
      - Iterate and set `book_id = null` only on notes that are actually linked to this book (defensive check, L1182–L1191) to prevent accidental unlinking.
      - Move the folder to root and unlink it from the book via `updateFolderWithValidation({ parent_id: null, book_id: null })` (L1193–L1201). Failing this is considered critical and throws.
    - If no notes: delete the empty folder (L1203–L1211). Failing this is also critical.
  - Regardless of folder outcome, delete the book row (L1217–L1225) and emit `notifyBookChange('delete', ...)` (L1226–L1233).
  - Returns a `{ success, id }` object from the DB deletion (L1235–L1236) or throws a detailed error on failure (L1238–L1244).

Nuance: Carefully preserves user notes by unlinking rather than deleting them, and leaves a now-independent folder in the root if it had content, aligning with user expectations for data safety.

---

## Aggregate books with media metadata (L1247–L1307)

- `getBooksWithMetadata()` enhances the raw book list:
  - Fetch `books` with note counts (L1249–L1254).
  - Bulk-fetch cover `media_files` (L1260–L1272) in a single query to avoid N+1. Keep only the most recent cover per book (L1274–L1279).
  - Map each book to include:
    - `addedDate`: JS `Date` from `created_at` (L1294–L1295).
    - `rating`: sanitized to [0,5] or undefined if invalid (L1296–L1297).
    - `cover_media_url`: resolved via `filePathToMediaUrl` (L1257–L1259, L1289–L1299).
  - Returns the enhanced list; errors are surfaced with context (L1302–L1306).

Nuance: This is an optimized path for front-end rendering, eliminating extra DB calls for covers.

---

## Hybrid search (local + online) (L1309–L1350)

- `searchBooksHybrid(searchTerm, includeOnline = true, onlineLimit = 10)`:
  - Queries the local DB first (L1319–L1321).
  - Optionally calls `searchBooksOnline` with local results to assist deduplication in relevance scoring (L1324–L1335).
  - Returns structured `{ localResults, onlineResults }` (L1342–L1345); errors are wrapped (L1346–L1349).

Nuance: Surfaces local results immediately while enriching with high-quality OL results ranked by the enhanced scorer.

---

## Add book from OpenLibrary result (L1352–L1489)

- `addBookFromOpenLibrary(searchResult)` safeguards and enriches a user-selected result:
  - Prevents duplicates by checking ISBN and OLID (L1355–L1367).
  - If `olid` exists, fetch detailed work/edition data (L1373–L1376). Cover selection logic prioritizes user custom covers and high-quality `cover_i` images (L1377–L1393):
    - Flags `hasCustomCover`, `userModifiedCover`, `preventCoverOverride` protect user selection from being overwritten.
  - Critical preservation: If the user-edited fields differ from fetched details, prefer the user’s values (L1395–L1412): title, authors, language, genres, first publish year.
  - On detail fetch error or timeout, fall back to an object assembled from `searchResult` using the same cover precedence rules (L1414–L1451).
  - If no `olid`, build book data directly from `searchResult` applying cover precedence (L1453–L1481).
  - Finally, create the book via `createBookWithValidation(bookData, true)` (L1484), which handles download/processing of covers and folder creation, and emits proper events.

Nuance: The function balances API-derived accuracy with user intent and customization, preserving any manual edits and avoiding duplicate additions.

---

## Ensure folders for all books (L1491–L1568)

- `ensureFoldersForAllBooks()` repairs missing folder associations:
  - Retrieves all books (L1499–L1501), then for each:
    - Checks for an existing folder via `getBookFolder` (L1511–L1517).
    - If missing, creates one under Books root using `sanitizeBookTitle` and linking `book_id` (L1520–L1546).
  - Returns a summary `{ created, errors, total }` (L1555–L1561). Errors are logged and counted but do not abort the entire process (L1563–L1567).

Nuance: Includes extensive logging tagged as “duplicate folder bug debug” to aid diagnosing prior duplication issues.

---

## Find books without folders (L1570–L1588)

- `getBooksWithoutFolders()` uses a LEFT JOIN to select books with no matching folder (L1573–L1578), logs count, and returns the list (L1580–L1582). Errors are surfaced with context (L1584–L1587).

---

## Cleanup base64 cover URLs (L1590–L1637)

- `cleanupBase64CoverUrls()` migrates base64-encoded covers to media files (for sync/size optimization):
  - Finds books whose `cover_url` starts with `data:` (L1595–L1599).
  - For each:
    - If a `media_files` cover already exists for the book, simply clear `cover_url` (L1611–L1615).
    - Otherwise, decode the base64, save a cover file via `saveBookCover`, and then clear `cover_url` (L1616–L1621).
  - Returns `{ processed, errors }` with logs (L1630–L1632), or throws a contextual error (L1633–L1636).

Nuance: Reduces database payload and sync manifest size by relying on file-backed media instead of inline base64.

---

## Default export (L1639–L1668)

- Exports a cohesive API object combining CRUD, search, enrichment, repair, and utility functions under clear names, including the replacement delete function `deleteBookAndHandleFolder` bound to the `deleteBook` key to preserve external API compatibility while changing behavior (L1650).

---

## Architectural notes and reasoning highlights

- **Network robustness**: Timeouts are generous and consistent; direct Node HTTP is used for image download control. Author fetches include retry with delay, while errors degrade gracefully.
- **User intent preservation**: The design favors user-provided/edited values (covers, titles, language) and prevents duplicates.
- **Storage optimization**: Early conversion of base64 covers to files, plus a cleanup utility, prevents database bloat and sync performance issues.
- **Safety on deletion**: Notes are unlinked rather than deleted when removing a book; folders with content become unlinked roots instead of being destroyed.
- **Ranking quality**: The scoring model balances exact and fuzzy matching, author signals, cover availability, recency, and metadata richness. Local library presence downranks duplicates in online results.
- **Operational hygiene**: Batch processing, caching, and multi-query optimizations reduce resource strain and latency.

---

## Potential edge cases and suggestions

- **Redirect loops for covers**: Max redirects is 5; consider logging the chain for diagnostics.
- **ISBN parsing**: Recognizes only clean 10/13-digit forms; could optionally validate checksums for stronger filtering.
- **Title/author normalization**: The fuzzy functions do not remove punctuation beyond spaces; additional normalization (e.g., diacritics folding) could improve matches further.
- **Cover MIME type**: When converting external covers to base64, assumes JPEG; consider detecting MIME via response headers for PNG/WebP.
- **Error surfaces**: A few `console.warn`/`console.error` branches could return contextual error codes for the UI to present more actionable messages.
- **Cache key**: Includes `localBooks.length` only; dedupe could be more precise by including identifiers of local items or a hash.

---

## Quick reference: exported functions

- `searchBooksOnline(query, limit, localBooks)` – Online search with ranking and cache.
- `searchBooksHybrid(searchTerm, includeOnline, onlineLimit)` – Combined local/online search.
- `getBookDetailsFromOpenLibrary(olid)` – Work + edition aggregation with retries/fallbacks.
- `addBookFromOpenLibrary(searchResult)` – Safe import with user intent preservation.
- `createBookWithValidation(bookData, downloadCover)` – Validated create + cover processing + folder creation.
- `updateBookWithValidation(id, updates)` – Update + change hook notification.
- `deleteBookAndHandleFolder(bookId)` – Safe remove with folder/notes handling.
- `getBooksWithMetadata()` – Optimized fetch for UI with cover URLs.
- `ensureFoldersForAllBooks()` – Repair missing folders.
- `getBooksWithoutFolders()` – Find orphaned books.
- `checkAndDownloadMissingCovers()` – Backfill missing cover files.
- `cleanupBase64CoverUrls()` – Migrate inline covers to files.
- `downloadCoverImageData(url)` – Robust binary fetch with redirects/timeouts.
- `getEnhancedStringSimilarity(a, b)` – Composite similarity metric used in scoring.

---

If you want this report kept in sync with future edits (lines may drift), consider generating a summarized, non-line-numbered version or adding a small codegen task to refresh references as part of CI.


