import { z } from 'zod';

// Main Book schema based on the database structure
export const BookSchema = z.object({
  id: z.number().optional(),
  title: z.string().min(1, { message: "Title is required" }),
  author: z.string().nullable().optional(),
  isbn: z.string().nullable().optional(),
  // Accept http(s), data URLs, custom noti-media scheme, or empty string
  cover_url: z
    .string()
    .refine(
      (val) =>
        val === '' ||
        /^https?:\/\//i.test(val) ||
        /^data:image\//i.test(val) ||
        /^noti-media:\/\//i.test(val),
      { message: 'cover_url must be http(s), data URL, noti-media URL, or empty' }
    )
    .nullable()
    .optional(),
  publication_date: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  page_count: z.number().int().min(0).nullable().optional(),
  current_page: z.number().int().min(0).nullable().optional(),
  rating: z.number().min(0).max(5).nullable().optional(),
  language: z.string().nullable().optional(),
  genres: z.string().nullable().optional(),
  olid: z.string().nullable().optional(),
  // Allow null in persisted data for backward compatibility, but do not default here
  status: z.enum(['unread', 'reading', 'completed', 'to-read']).nullable().optional(),
  custom_fields: z.string().nullable().optional(),
  created_at: z
    .string()
    .refine((s) => {
      // Accept ISO 8601 with 'Z' or timezone offsets, or SQLite CURRENT_TIMESTAMP format
      const isoZ = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z$/i.test(s);
      const isoOffset = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?([+-]\d{2}:?\d{2})$/i.test(s);
      const sqliteTs = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}(?:\.\d+)?$/.test(s);
      return isoZ || isoOffset || sqliteTs;
    }, { message: 'created_at must be ISO 8601 (with Z or offset) or SQLite timestamp' })
    .optional(),
  updated_at: z
    .string()
    .refine((s) => {
      const isoZ = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?Z$/i.test(s);
      const isoOffset = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?([+-]\d{2}:?\d{2})$/i.test(s);
      const sqliteTs = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}(?:\.\d+)?$/.test(s);
      return isoZ || isoOffset || sqliteTs;
    }, { message: 'updated_at must be ISO 8601 (with Z or offset) or SQLite timestamp' })
    .optional(),
});

// OpenLibrary Search Result schema
export const OpenLibrarySearchResultSchema = z.object({
  title: z.string(),
  author_name: z.array(z.string()).optional(),
  isbn: z.array(z.string()).optional(),
  cover_i: z.number().optional(),
  cover_edition_key: z.string().optional(),
  first_publish_year: z.number().optional(),
  language: z.array(z.string()).optional(),
  edition_count: z.number().optional(),
  key: z.string().optional(),
  subject: z.array(z.string()).optional(),
  publisher: z.array(z.string()).optional(),
  publish_year: z.array(z.number()).optional(),
  oclc: z.array(z.string()).optional(),
  lccn: z.array(z.string()).optional(),
  olid: z.string().optional(),
});

// OpenLibrary Work Details schema
export const OpenLibraryWorkDetailsSchema = z.object({
  title: z.string(),
  authors: z.array(z.object({
    author: z.object({
      key: z.string(),
    }),
    type: z.object({
      key: z.string(),
    }),
  })).optional(),
  description: z.union([
    z.string(),
    z.object({
      type: z.string(),
      value: z.string(),
    })
  ]).optional(),
  subjects: z.array(z.string()).optional(),
  first_publish_date: z.string().optional(),
  key: z.string(),
  covers: z.array(z.number()).optional(),
});

// OpenLibrary Edition Details schema
export const OpenLibraryEditionDetailsSchema = z.object({
  title: z.string(),
  authors: z.array(z.object({
    key: z.string(),
  })).optional(),
  isbn_10: z.array(z.string()).optional(),
  isbn_13: z.array(z.string()).optional(),
  number_of_pages: z.number().optional(),
  publish_date: z.string().optional(),
  publishers: z.array(z.string()).optional(),
  covers: z.array(z.number()).optional(),
  languages: z.array(z.object({
    key: z.string(),
  })).optional(),
  description: z.union([
    z.string(),
    z.object({
      type: z.string(),
      value: z.string(),
    })
  ]).optional(),
  key: z.string(),
});

// Enhanced Book Search Result schema (extends OpenLibrary with additional fields)
export const BookSearchResultSchema = OpenLibrarySearchResultSchema.extend({
  cover_url: z.string().optional(),
  description: z.string().optional(),
  genres: z.string().optional(),
  isbn_primary: z.string().optional(),
  relevanceScore: z.number().optional(),
  hasCustomCover: z.boolean().optional(),
  userModifiedCover: z.boolean().optional(),
  preventCoverOverride: z.boolean().optional(),
});

// Book with Note Count schema (extends Book with additional metadata)
export const BookWithNoteCountSchema = BookSchema.extend({
  notesCount: z.number(),
  recentNote: z.object({
    id: z.number(),
    title: z.string(),
    last_viewed_at: z.string(),
  }).optional(),
  addedDate: z.date().optional(),
  cover_media_url: z.string().nullable().optional(),
  isLoading: z.boolean().optional(),
});

// Schema for creating a book (omits generated fields)
export const CreateBookSchema = BookSchema.omit({ 
  id: true, 
  created_at: true, 
  updated_at: true 
});

// Schema for updating a book (all fields are optional except id)
export const UpdateBookSchema = BookSchema.partial().extend({
  id: z.number(), // id is required for updates
});

// Schema for search results
export const SearchResultSchema = z.object({
  books: z.array(BookSchema),
  total: z.number(),
  query: z.string(),
  source: z.enum(['local', 'online', 'hybrid']),
});

// Schema for hybrid search results
export const HybridSearchResultSchema = z.object({
  localResults: z.array(BookSchema),
  onlineResults: z.array(BookSearchResultSchema),
});

// OpenLibrary Author Details schema
export const OpenLibraryAuthorDetailsSchema = z.object({
  name: z.string(),
  key: z.string(),
});

// OpenLibrary ISBN API response schema (books by bibkeys)
export const OpenLibraryISBNDataSchema = z.object({
  title: z.string(),
  authors: z.array(z.object({
    name: z.string(),
  })).optional(),
  cover: z.object({
    small: z.string().optional(),
    medium: z.string().optional(),
    large: z.string().optional(),
  }).optional(),
  publish_date: z.string().optional(),
  identifiers: z.object({
    isbn_10: z.array(z.string()).optional(),
    isbn_13: z.array(z.string()).optional(),
  }).optional(),
  publishers: z.array(z.object({
    name: z.string(),
  })).optional(),
  languages: z.array(z.object({
    key: z.string(),
  })).optional(),
});

// Schema for the full ISBN API response (keyed by ISBN)
export const OpenLibraryISBNResponseSchema = z.record(z.string(), OpenLibraryISBNDataSchema);
