<?xml version="1.0" encoding="UTF-8"?>
<refactoring-phase id="4" name="Comprehensive Testing">
  <metadata>
    <estimated-duration>3 days</estimated-duration>
    <risk-level>low</risk-level>
    <dependencies>
      <dependency type="phase">phase-3</dependency>
      <dependency type="service">OpenLibraryService from Phase 2</dependency>
      <dependency type="service">CoverService from Phase 2</dependency>
      <dependency type="service">SearchService from Phase 3</dependency>
      <dependency type="service">BookService from Phase 3</dependency>
    </dependencies>
    <next-phase>phase-5</next-phase>
  </metadata>

  <objectives>
    <primary-goal>Build a robust, automated safety net that validates the correctness of the new implementation and provides confidence to delete legacy code</primary-goal>
    <secondary-goals>
      <goal>Achieve >90% code coverage for all services</goal>
      <goal>Validate end-to-end flows for critical user paths</goal>
      <goal>Prove backward compatibility with existing functionality</goal>
      <goal>Establish automated regression detection</goal>
      <goal>Document testing patterns for future development</goal>
    </secondary-goals>
  </objectives>

  <milestones>
    <milestone id="4.1" name="Schema Validation Tests">
      <description>All Zod schemas are thoroughly tested for validation rules</description>
      <validation>Both success and failure cases are covered for each schema</validation>
    </milestone>
    <milestone id="4.2" name="Service Unit Tests">
      <description>All services achieve >90% code coverage with isolated testing</description>
      <validation>Each service is tested independently with mocked dependencies</validation>
    </milestone>
    <milestone id="4.3" name="Integration Tests">
      <description>Critical user flows are validated end-to-end</description>
      <validation>Services work together correctly in realistic scenarios</validation>
    </milestone>
    <milestone id="4.4" name="Compatibility Tests">
      <description>New implementation produces identical results to legacy code</description>
      <validation>Automated comparison confirms no regressions</validation>
    </milestone>
  </milestones>

  <tasks>
    <task id="4.1" priority="critical" estimated-time="4 hours">
      <name>Write Schema Validation Tests</name>
      <description>Create comprehensive tests for all Zod schemas</description>
      <files-to-create>
        <file>electron/main/api/books-api/__tests__/schemas.test.ts</file>
      </files-to-create>
      <implementation-details>
        <step>Set up Jest testing environment</step>
        <step>Import all schemas from book.schemas.ts</step>
        <step>Create valid test data for each schema</step>
        <step>Create invalid test data for each validation rule</step>
        <step>Test success cases with expect().not.toThrow()</step>
        <step>Test failure cases with expect().toThrow()</step>
        <step>Test edge cases and boundary conditions</step>
        <step>Verify error messages are meaningful</step>
      </implementation-details>
      <test-coverage>
        <schema name="BookSchema">
          <valid-cases>
            <case>Complete book with all fields</case>
            <case>Minimal book with only required fields</case>
            <case>Book with null optional fields</case>
          </valid-cases>
          <invalid-cases>
            <case>Missing required title</case>
            <case>Invalid URL format for cover_url</case>
            <case>Rating outside 0-5 range</case>
            <case>Negative page numbers</case>
          </invalid-cases>
        </schema>
        <schema name="OpenLibraryBookSchema">
          <valid-cases>
            <case>Complete OpenLibrary response</case>
            <case>Minimal response with required fields</case>
          </valid-cases>
          <invalid-cases>
            <case>Missing required title</case>
            <case>Invalid key format</case>
          </invalid-cases>
        </schema>
        <schema name="CreateBookSchema">
          <valid-cases>
            <case>New book data without generated fields</case>
          </valid-cases>
          <invalid-cases>
            <case>Including id field (should be omitted)</case>
            <case>Including created_at field</case>
          </invalid-cases>
        </schema>
      </test-coverage>
      <success-criteria>
        <criterion>All schemas have comprehensive test coverage</criterion>
        <criterion>Both success and failure paths are tested</criterion>
        <criterion>Error messages are validated</criterion>
        <criterion>Edge cases are covered</criterion>
      </success-criteria>
      <risks>
        <risk severity="low">Incomplete test coverage</risk>
      </risks>
    </task>

    <task id="4.2" priority="critical" estimated-time="12 hours">
      <name>Write Service Unit Tests</name>
      <description>Create isolated unit tests for each service with mocked dependencies</description>
      <files-to-create>
        <file>electron/main/api/books-api/__tests__/openlibrary.service.test.ts</file>
         <file>electron/main/api/books-api/__tests__/cover.service.test.ts</file>
        <file>electron/main/api/books-api/__tests__/search.service.test.ts</file>
        <file>electron/main/api/books-api/__tests__/book.service.test.ts</file>
        <file>electron/main/api/books-api/__tests__/maintenance.service.test.ts</file>
      </files-to-create>
      <implementation-details>
        <step>Set up Jest with mocking capabilities</step>
        <step>Create mock implementations for all dependencies</step>
        <step>Test each service method independently</step>
        <step>Verify correct dependency interactions</step>
        <step>Test error handling scenarios</step>
        <step>Achieve >90% code coverage</step>
        <step>Use dependency injection for testability</step>
      </implementation-details>
      <service-tests>
        <service name="OpenLibraryService">
          <mocks>
            <mock name="httpClient">Mock axios responses</mock>
            <mock name="logger">Mock logging calls</mock>
          </mocks>
          <test-cases>
            <case>searchBooks returns validated data</case>
            <case>searchBooks handles API errors</case>
            <case>searchBooks validates response with Zod</case>
            <case>getBookDetails fetches single book</case>
            <case>getCoverUrl generates correct URLs</case>
          </test-cases>
        </service>
        <service name="CoverService">
          <mocks>
            <mock name="httpClient">Mock image download</mock>
            <mock name="sharp">Mock image processing</mock>
            <mock name="logger">Mock logging calls</mock>
          </mocks>
           <test-cases>
             <case>processCoverUrl handles http(s), data:, and noti-media:// sources</case>
             <case>processCoverUrl preserves original format by default</case>
             <case>processCoverUrl handles download errors</case>
             <case>standardizeImage calls Sharp with correct parameters when enabled</case>
             <case>validateImageBuffer enforces 10MB limit</case>
             <case>validateImageBuffer rejects invalid images</case>
           </test-cases>
        </service>
        <service name="MaintenanceService">
          <mocks>
            <mock name="CoverService">Mock image download/process</mock>
            <mock name="media-api">Mock file save</mock>
            <mock name="database-api">Mock book/cover queries</mock>
            <mock name="folders-api">Mock folder operations</mock>
          </mocks>
          <test-cases>
            <case>checkAndDownloadMissingCovers downloads and saves missing covers</case>
            <case>ensureFoldersForAllBooks creates missing book folders and respects uniqueness</case>
            <case>getBooksWithoutFolders returns correct list</case>
            <case>cleanupBase64CoverUrls writes media files and clears DB field</case>
          </test-cases>
        </service>
        <service name="SearchService">
          <mocks>
            <mock name="openLibraryService">Mock online search</mock>
            <mock name="Fuse">Mock Fuse.js functionality</mock>
            <mock name="logger">Mock logging calls</mock>
          </mocks>
          <test-cases>
            <case>searchLocal uses Fuse.js correctly</case>
            <case>searchOnline delegates to OpenLibrary service</case>
            <case>searchHybrid merges local and online results</case>
            <case>mergeResults handles duplicate detection</case>
          </test-cases>
        </service>
        <service name="BookService">
          <mocks>
            <mock name="coverService">Mock cover processing</mock>
            <mock name="searchService">Mock search functionality</mock>
            <mock name="databaseApi">Mock database operations</mock>
            <mock name="logger">Mock logging calls</mock>
          </mocks>
          <test-cases>
            <case>createBook validates input with Zod</case>
            <case>createBook delegates cover processing</case>
            <case>updateBook validates partial updates</case>
            <case>deleteBook handles cleanup properly</case>
            <case>Error handling for all CRUD operations</case>
          </test-cases>
        </service>
      </service-tests>
      <success-criteria>
        <criterion>Each service achieves >90% code coverage</criterion>
        <criterion>All dependencies are properly mocked</criterion>
        <criterion>Error scenarios are thoroughly tested</criterion>
        <criterion>Service interactions are verified</criterion>
      </success-criteria>
      <risks>
        <risk severity="medium">Complex mocking requirements</risk>
        <risk severity="low">Achieving high code coverage</risk>
      </risks>
    </task>

    <task id="4.3" priority="high" estimated-time="8 hours">
      <name>Write Integration Tests</name>
      <description>Create end-to-end tests for critical user workflows</description>
      <files-to-create>
        <file>electron/main/api/books-api/__tests__/integration/book-creation.integration.test.ts</file>
        <file>electron/main/api/books-api/__tests__/integration/book-search.integration.test.ts</file>
        <file>electron/main/api/books-api/__tests__/integration/book-management.integration.test.ts</file>
      </files-to-create>
      <implementation-details>
        <step>Set up in-memory SQLite database for testing</step>
        <step>Create temporary directories for file operations</step>
        <step>Use real services with minimal mocking</step>
        <step>Test complete user workflows</step>
        <step>Verify database state changes</step>
        <step>Verify file system operations</step>
        <step>Clean up test data after each test</step>
      </implementation-details>
      <integration-scenarios>
         <scenario name="Book Creation Flow">
          <description>Complete book creation with cover processing</description>
          <steps>
            <step>Call createBook with cover URL</step>
            <step>Verify book is saved to database</step>
             <step>Verify cover image is downloaded and saved locally</step>
             <step>Verify image is saved in original format with correct MIME/extension</step>
             <step>Verify optional standardization path when enabled</step>
          </steps>
        </scenario>
        <scenario name="Maintenance Utilities">
          <description>Verify maintenance flows continue to function</description>
          <steps>
            <step>Seed a book with base64 cover_url and no media_files cover</step>
            <step>Run cleanupBase64CoverUrls and verify media file is created and DB field cleared</step>
            <step>Seed books without folders and run ensureFoldersForAllBooks; verify one-to-one mapping</step>
          </steps>
        </scenario>
        <scenario name="Hybrid Search Flow">
          <description>Search combining local and online results</description>
          <steps>
            <step>Populate database with test books</step>
            <step>Perform hybrid search</step>
            <step>Verify local results from Fuse.js</step>
            <step>Verify online results from OpenLibrary</step>
            <step>Verify results are properly merged</step>
          </steps>
        </scenario>
        <scenario name="Book Update Flow">
          <description>Update book with new cover and metadata</description>
          <steps>
            <step>Create initial book</step>
            <step>Update with new cover URL</step>
            <step>Verify old cover is cleaned up</step>
            <step>Verify new cover is processed</step>
            <step>Verify database is updated</step>
          </steps>
        </scenario>
      </integration-scenarios>
      <success-criteria>
        <criterion>All critical user workflows are tested</criterion>
        <criterion>Database operations are verified</criterion>
        <criterion>File system operations are verified</criterion>
        <criterion>Services work together correctly</criterion>
      </success-criteria>
      <risks>
        <risk severity="medium">Test environment setup complexity</risk>
        <risk severity="low">Test data cleanup issues</risk>
      </risks>
    </task>
  </tasks>

  <validation-steps>
    <step id="1" name="Test Suite Execution">
      <description>Run all tests and verify they pass</description>
      <command>npm test</command>
      <expected-result>All tests pass with >90% coverage</expected-result>
    </step>
    <step id="2" name="Coverage Report">
      <description>Generate and review code coverage report</description>
      <command>npm run test:coverage</command>
      <expected-result>Coverage >90% for all services</expected-result>
    </step>
    <step id="3" name="Integration Verification">
      <description>Run integration tests with real database</description>
      <command>npm run test:integration</command>
      <expected-result>All integration scenarios pass</expected-result>
    </step>
    <step id="4" name="Compatibility Verification">
      <description>Run compatibility tests comparing old vs new</description>
      <command>npm run test:compatibility</command>
      <expected-result>New implementation matches old behavior</expected-result>
    </step>
  </validation-steps>

  <risk-assessment>
    <overall-risk>Low</overall-risk>
    <risk-factors>
      <factor name="Test Coverage Gaps" probability="medium" impact="medium">
        <mitigation>Comprehensive test planning and coverage monitoring</mitigation>
      </factor>
      <factor name="Mock Complexity" probability="medium" impact="low">
        <mitigation>Use established mocking patterns and libraries</mitigation>
      </factor>
      <factor name="Integration Environment" probability="low" impact="medium">
        <mitigation>Isolated test environment with proper cleanup</mitigation>
      </factor>
    </risk-factors>
  </risk-assessment>

  <additional-tasks>
    <task id="4.4" priority="critical" estimated-time="6 hours">
      <name>Write Backward Compatibility Tests</name>
      <description>Create tests that compare old and new implementations</description>
      <files-to-create>
        <file>electron/main/api/books-api/__tests__/compatibility.test.ts</file>
      </files-to-create>
      <implementation-details>
        <step>Import both old proxy and new API</step>
        <step>Create identical test scenarios for both</step>
        <step>Compare results for logical equivalence</step>
        <step>Test with various input combinations</step>
        <step>Verify error handling consistency</step>
        <step>Document any acceptable differences</step>
      </implementation-details>
      <compatibility-tests>
        <test name="Book Creation Comparison">
          <description>Compare createBook results between old and new</description>
          <input>Various book data payloads</input>
          <comparison>Database records and file system state</comparison>
        </test>
        <test name="Search Results Comparison">
          <description>Compare search results between implementations</description>
          <input>Various search terms and scenarios</input>
          <comparison>Result relevance and ordering</comparison>
        </test>
        <test name="Error Handling Comparison">
          <description>Compare error responses for invalid inputs</description>
          <input>Invalid data and error conditions</input>
          <comparison>Error types and messages</comparison>
        </test>
      </compatibility-tests>
      <success-criteria>
        <criterion>New implementation produces equivalent results</criterion>
        <criterion>Error handling is consistent</criterion>
        <criterion>Performance is maintained or improved</criterion>
        <criterion>All differences are documented and justified</criterion>
      </success-criteria>
      <risks>
        <risk severity="medium">Subtle behavioral differences</risk>
        <risk severity="low">Performance regression detection</risk>
      </risks>
    </task>

    <task id="4.5" priority="high" estimated-time="4 hours">
      <name>Performance Testing</name>
      <description>Verify new implementation maintains or improves performance</description>
      <files-to-create>
        <file>electron/main/api/books-api/__tests__/performance.test.ts</file>
      </files-to-create>
      <implementation-details>
        <step>Create performance benchmarks for key operations</step>
        <step>Measure execution time for both implementations</step>
        <step>Test with various data sizes</step>
        <step>Monitor memory usage patterns</step>
        <step>Identify performance improvements</step>
        <step>Document performance characteristics</step>
      </implementation-details>
      <performance-metrics>
        <metric name="Search Performance">
          <description>Time to complete search operations</description>
          <baseline>Current custom search implementation</baseline>
          <target>Equal or better with Fuse.js</target>
        </metric>
        <metric name="Image Processing Performance">
          <description>Time to process cover images</description>
          <baseline>Current raw HTTP processing</baseline>
          <target>Improved with Sharp library</target>
        </metric>
        <metric name="Memory Usage">
          <description>Memory consumption during operations</description>
          <baseline>Current implementation</baseline>
          <target>Reduced with optimized libraries</target>
        </metric>
      </performance-metrics>
      <success-criteria>
        <criterion>No performance regressions detected</criterion>
        <criterion>Image processing shows improvement</criterion>
        <criterion>Memory usage is optimized</criterion>
        <criterion>Search performance is maintained</criterion>
      </success-criteria>
      <risks>
        <risk severity="medium">Performance regression in search</risk>
        <risk severity="low">Memory usage increase</risk>
      </risks>
    </task>
  </additional-tasks>

  <test-infrastructure>
    <setup>
      <jest-config>
        <coverage-threshold>90%</coverage-threshold>
        <test-environment>node</test-environment>
        <setup-files>test-setup.ts</setup-files>
      </jest-config>
      <mock-setup>
        <global-mocks>
          <mock name="electron-log">Mock logging functionality</mock>
          <mock name="sharp">Mock image processing</mock>
          <mock name="axios">Mock HTTP requests</mock>
        </global-mocks>
      </mock-setup>
    </setup>
    <utilities>
      <test-data-factory>Generate consistent test data</test-data-factory>
      <database-helper>Manage test database lifecycle</database-helper>
      <file-system-helper>Manage temporary files and cleanup</file-system-helper>
    </utilities>
  </test-infrastructure>
</refactoring-phase>
