import { promises as fs } from 'fs';
import path from 'path';

async function ensureDir(dir) {
  await fs.mkdir(dir, { recursive: true });
}

function getLangHint(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  switch (ext) {
    case '.ts':
    case '.mts':
    case '.cts':
    case '.d.ts':
      return 'ts';
    case '.js':
    case '.mjs':
    case '.cjs':
      return 'js';
    case '.tsx':
      return 'tsx';
    case '.jsx':
      return 'jsx';
    case '.vue':
      return 'vue';
    case '.json':
      return 'json';
    case '.css':
    case '.scss':
    case '.sass':
      return 'css';
    case '.md':
      return 'md';
    case '.txt':
      return 'text';
    default:
      return 'text';
  }
}

const INCLUDED_TEXT_EXTS = new Set([
  '.vue', '.ts', '.tsx', '.js', '.jsx', '.json', '.css', '.scss', '.sass', '.md', '.txt', '.d.ts'
]);

const EXCLUDED_EXTS = new Set([
  '.map', '.ico', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.icns', '.exe', '.bin', '.node', '.pdf', '.webp', '.avif', '.bmp'
]);

function shouldInclude(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  if (EXCLUDED_EXTS.has(ext)) return false;
  if (INCLUDED_TEXT_EXTS.has(ext)) return true;
  // Also include files with no extension that are likely text (heuristic)
  return ext === '';
}

async function isDirectory(p) {
  try {
    const st = await fs.stat(p);
    return st.isDirectory();
  } catch {
    return false;
  }
}

async function walkDir(srcDir, onFile) {
  const entries = await fs.readdir(srcDir, { withFileTypes: true });
  for (const entry of entries) {
    const fullPath = path.join(srcDir, entry.name);
    if (entry.isDirectory()) {
      await walkDir(fullPath, onFile);
    } else if (entry.isFile()) {
      await onFile(fullPath);
    }
  }
}

function asMdOutputPath(srcRoot, destRoot, filePath) {
  const rel = path.relative(srcRoot, filePath);
  // Keep original filename.ext and append .md
  const mdRel = rel + '.md';
  return path.join(destRoot, mdRel);
}

async function convertFile(srcRoot, destRoot, filePath) {
  if (!shouldInclude(filePath)) return;

  let content;
  try {
    content = await fs.readFile(filePath, 'utf8');
  } catch {
    // Skip unreadable files
    return;
  }

  const lang = getLangHint(filePath);
  const outPath = asMdOutputPath(srcRoot, destRoot, filePath);
  const outDir = path.dirname(outPath);
  await ensureDir(outDir);

  const relPath = path.relative(process.cwd(), filePath).replace(/\\/g, '/');

  const md = `---
Original path: ${relPath}
---

\`\`\`${lang}
${content}
\`\`\`
`;

  await fs.writeFile(outPath, md, 'utf8');
}

async function main() {
  const srcRoot = process.argv[2] || 'BackEndClasses';
  const destRoot = process.argv[3] || 'BackEndClasses-md';

  const absSrc = path.isAbsolute(srcRoot) ? srcRoot : path.join(process.cwd(), srcRoot);
  const absDest = path.isAbsolute(destRoot) ? destRoot : path.join(process.cwd(), destRoot);

  if (!(await isDirectory(absSrc))) {
    console.error(`Source directory does not exist or is not a directory: ${absSrc}`);
    process.exit(1);
  }

  await ensureDir(absDest);

  console.log(`Converting folder '${srcRoot}' to markdown under '${destRoot}'...`);

  let count = 0;
  await walkDir(absSrc, async (filePath) => {
    const before = count;
    await convertFile(absSrc, absDest, filePath);
    if (before === count) {
      // If convertFile decided to include, increment.
      // We can't know easily here; instead, increment inside convertFile would require changing signature.
      // Simpler: re-check include here.
      if (shouldInclude(filePath)) count += 1;
    }
  });

  console.log(`Completed. ${count} file(s) converted.`);
}

main().catch((err) => {
  console.error(err);
  process.exit(1);
});
