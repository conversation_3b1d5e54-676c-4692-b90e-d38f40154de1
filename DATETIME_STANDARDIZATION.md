## Noti Datetime Standardization and Refactor Plan

This document catalogs every distinct timestamp/date pattern in the codebase (with citations), explains why they are inconsistent, and proposes a precise, verifiable, app‑level standardization plan that requires no database migrations. The intent is to align with the ongoing refactor towards componentized APIs and Zod validation while avoiding schema changes.

---

### Goals

- Use a single canonical representation for persisted timestamps: ISO 8601 UTC strings with milliseconds and a trailing Z (example: `2025-01-15T10:20:30.123Z`).
- Keep any duration fields numeric (seconds or milliseconds; this document uses seconds to match current timer code).
- Do not rely on SQLite `CURRENT_TIMESTAMP` for persisted data; set times in application code.
- Avoid DB migrations; leave schema defaults in place but treat them as unused fallback.
- Make date-based queries deterministic and timezone-explicit.

---

## 1) Current timestamp and date usage (verifiable)

#### 1.1 App-side ISO 8601 timestamps via `new Date().toISOString()`
- Notes create/update and `last_viewed_at` are explicitly set from app code:
```160:176:electron/main/database/database-api.ts
// Use ISO timestamp format for consistency with backup system
const now = new Date().toISOString();
...
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
...
 type, color, order, now, now, now
```
```255:305:electron/main/database/database-api.ts
// Build update query dynamically ...
let query = 'UPDATE notes SET updated_at = ?';
const params: any[] = [new Date().toISOString()];
...
// Update the last_viewed_at when a note is updated - use ISO string for consistency
query += ', last_viewed_at = ?';
params.push(new Date().toISOString());
```
- Folders and books use ISO at creation and update:
```341:349:electron/main/database/database-api.ts
const now = new Date().toISOString();
...
INSERT INTO folders (..., created_at, updated_at)
VALUES (..., now, now)
```
```571:586:electron/main/database/database-api.ts
const now = new Date().toISOString();
INSERT INTO books (..., created_at, updated_at)
VALUES (..., now, now)
```
- Media files use ISO for `created_at`:
```104:112:electron/main/api/media-api.ts
const result = dbRun(query, [
  noteId, bookId, filePath, uniqueFileName, fileType, fileSize, isCover ? 1 : 0, new Date().toISOString()
]);
```
- Timer: starts and settings use ISO in many places:
```79:88:electron/main/api/timer-api.ts
const startTime = new Date().toISOString();
...
VALUES (?, ?, ?, ?, ?, ?, ?, 0, ?, ?)
[ startTime, 'work', ..., startTime, startTime ]
```
```582:598:electron/main/api/timer-api.ts
const now = new Date().toISOString();
...
INSERT INTO timer_settings (..., created_at, updated_at) VALUES (..., now, now)
```

#### 1.2 DB-side timestamps via SQLite `CURRENT_TIMESTAMP`
- Schema defaults (space-formatted `YYYY-MM-DD HH:MM:SS`) are present across tables:
```108:116:electron/main/database/database.ts
... last_viewed_at TIMESTAMP,
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
```
```146:147:electron/main/database/database.ts
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
```
- Example insert that relies on DB default at bootstrap:
```445:447:electron/main/database/database.ts
INSERT INTO folders (name, parent_id, color, created_at, updated_at)
VALUES ('Books', NULL, '#4285F4', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
```
- Settings API uses DB to set/update timestamps:
```85:92:electron/main/api/settings-api.ts
UPDATE settings SET value_json = ?, category = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?
INSERT INTO settings (key, value_json, category, updated_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP)
```
- Recent items API uses DB to set/update `viewed_at`:
```56:66:electron/main/api/recent-items-api.ts
UPDATE recent_items SET viewed_at = CURRENT_TIMESTAMP WHERE note_id = ?
INSERT INTO recent_items (note_id, viewed_at) VALUES (?, CURRENT_TIMESTAMP)
```
```88:99:electron/main/api/recent-items-api.ts
UPDATE recent_items SET viewed_at = CURRENT_TIMESTAMP WHERE book_id = ?
INSERT INTO recent_items (book_id, viewed_at) VALUES (?, CURRENT_TIMESTAMP)
```
- Timer completes compute end/duration using DB time:
```106:115:electron/main/api/timer-api.ts
SET
  end_time = CURRENT_TIMESTAMP,
  duration = CAST((julianday(CURRENT_TIMESTAMP) - julianday(start_time)) * 86400 AS INTEGER),
  is_completed = 1,
  updated_at = CURRENT_TIMESTAMP
```
```284:303:electron/main/api/timer-api.ts
UPDATE pomodoro_cycles
SET end_time = CURRENT_TIMESTAMP,
    duration = CAST((julianday(CURRENT_TIMESTAMP) - julianday(start_time)) * 86400 AS INTEGER),
    completed = 1
...
UPDATE timer_sessions SET pomodoro_cycles_completed = pomodoro_cycles_completed + 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?
```
- **Additional timer API CURRENT_TIMESTAMP usage:**
```286:289:electron/main/api/timer-api.ts
SET end_time = CURRENT_TIMESTAMP,
    duration = CAST((julianday(CURRENT_TIMESTAMP) - julianday(start_time)) * 86400 AS INTEGER),
    completed = 1
```
```300:301:electron/main/api/timer-api.ts
UPDATE timer_sessions SET pomodoro_cycles_completed = pomodoro_cycles_completed + 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?
```
```400:403:electron/main/api/timer-api.ts
SET end_time = CURRENT_TIMESTAMP,
    duration = CAST((julianday(CURRENT_TIMESTAMP) - julianday(start_time)) * 86400 AS INTEGER),
    is_completed = 1,
    updated_at = CURRENT_TIMESTAMP
```
```717:719:electron/main/api/timer-api.ts
dbRun('UPDATE timer_sessions SET pomodoro_cycles_completed = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
    [session.actual_pomodoro_count, session.id])
```
- **Schema migration with CURRENT_TIMESTAMP:**
```370:372:electron/main/database/database.ts
try {
    db.exec(`ALTER TABLE timer_settings ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`);
} catch (alterErr) { ... }
```

#### 1.3 Date-based querying in SQL (mixed timezone semantics)
- Date range filters use SQLite `date()` on `start_time` (UTC by default):
```475:487:electron/main/api/timer-api.ts
WHERE date(ts.start_time) BETWEEN date(?) AND date(?)
```
- “Today” uses `date('now','localtime')` which mixes local-time “today” with UTC-anchored `start_time`:
```506:510:electron/main/api/timer-api.ts
SELECT * FROM timer_sessions WHERE date(start_time) = date('now', 'localtime') ORDER BY start_time DESC
```
- Statistics use `date(start_time)` similarly:
```523:542:electron/main/api/timer-api.ts
WHERE date(start_time) BETWEEN date(?) AND date(?)
AND is_completed = 1
```

#### 1.4 Other usage patterns
- Hooks generate `Date` and serialize deletes as ISO:
```75:83:electron/main/database/database-hooks.ts
const changeEvent: DatabaseChangeEvent = {
  ...,
  timestamp: new Date(),
  ...
};
```
```117:123:electron/main/database/database-hooks.ts
this.pendingDeletions.push({
  ..., deletedAt: changeEvent.timestamp.toISOString(), ...
});
```
- **Front-end and mock layer usage (extensive):**
```src/types/mock-api.ts (21, 35, 69, 104, 148, 216, 235, 259, 302, 392, 470, 584, 637, 640, 663, 754, 757, 810, 829, 831, 845, 849, 877)
new Date().toISOString() for all mock timestamps
Date.now() for generating mock IDs (569, 826, 1090)
new Date().toLocaleDateString() for UI formatting (87)
```
- **State management and caching:**
```src/stores/notesViewStore.ts (36, 47, 78)
Date.now() for view state restoration timing
```
```src/stores/timerStore.ts (571, 573)
new Date() and toLocaleDateString() for session name generation
```
- **Performance monitoring:**
```src/utils/pageLoadMonitor.ts (87, 119, 239)
Date.now() for navigation timing metrics
new Date().toISOString() for metric serialization
```
- **Discord RPC integration:**
```public/discord-rpc-api.ts (64, 65, 73, 82, 92)
Date.now() for idle timing and session tracking
new Date().toISOString() for logging
```
- **Year validation in external APIs:**
```electron/main/api/books-api.ts (498, 953)
const currentYear = new Date().getFullYear();
```
```electron/main/api/books-api/services/openlibrary.service.ts (471, 736)
const currentYear = new Date().getFullYear();
```
- **Export and diagnostic timestamping:**
```electron/main/api/notes-api.ts (126, 262, 747, 868, 892, 952, 1063, 1390, 1547)
new Date().toLocaleDateString() and toLocaleString() for exports
new Date().toISOString() for metadata and file naming
```
- **Cache TTL management:**
```electron/main/api/books-api.ts (597)
Date.now() for cache timestamp validation
```
```electron/main/api/books-api/services/openlibrary.service.ts (81)
Date.now() for cache expiration checks
```
- Locale formatting for UI/logging (not persisted):
```146:146:electron/main/index.ts
win?.webContents.send('main-process-message', new Date().toLocaleString())
```
```src/main.ts (39)
new Date().toLocaleTimeString() for preload completion logging
```
- `Date.now()` for non-persisted identifiers/file names and metrics:
```77:83:electron/main/api/media-api.ts
const timestamp = Date.now();
const uniqueFileName = `${timestamp}-${fileName}`;
```
```src/preload.ts (76)
completedAt: Date.now() for performance metrics
```
- **Unused third-party date library:**
```package.json (88)
"date-fns": "^4.1.0" - installed but completely unused in codebase
```

#### 1.5 Type declarations expect ISO strings
- Timer-related interfaces explicitly call out ISO strings:
```186:196:src/types/electron-api.d.ts
export interface TimerSession {
  ...
  start_time: string; // ISO 8601 format
  end_time?: string | null;
  duration?: number | null; // Duration in seconds
  ...
}
```

---

## 2) What’s inconsistent and why it matters

- Mixed time sources for “now”:
  - App code uses ISO (`new Date().toISOString()`), DB uses `CURRENT_TIMESTAMP`. These can differ by milliseconds and timezone handling, and they produce different string formats.
- Mixed storage formats:
  - App writes ISO `YYYY-MM-DDTHH:mm:ss.SSSZ`.
  - DB defaults and DB-written times are `YYYY-MM-DD HH:MM:SS` (no T, no Z, no milliseconds).
- Mixed timezone semantics in queries:
  - Some queries compare `date(start_time)` (UTC) to `date('now','localtime')` (local), which can shift edge cases at day boundaries.
- Duration computed in DB vs app:
  - Timer uses julianday against `CURRENT_TIMESTAMP` in DB to compute durations, while other modules set timestamps in app.
- Schema/API friction:
  - `theme_settings` table schema defines only `updated_at`, but code attempts to insert `created_at` as well.
```160:166:electron/main/database/database.ts
CREATE TABLE IF NOT EXISTS theme_settings (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  theme_name TEXT NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT 0,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)
```
```149:152:electron/main/api/settings-api.ts
INSERT INTO theme_settings (theme_name, is_active, created_at, updated_at)
VALUES (?, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
```
- **Bootstrap vs runtime timestamp patterns:**
  - Bootstrap folder creation uses `CURRENT_TIMESTAMP` directly in SQL (acceptable for setup)
  - Runtime business logic should avoid this pattern but currently doesn't consistently
- **Unused dependency bloat:**
  - `date-fns` library installed but never imported or used anywhere
  - Adds unnecessary bundle weight (~100KB) with zero benefit
- **Front-end/backend timezone handling mismatch:**
  - Backend standardizes on UTC ISO strings for persistence
  - Front-end uses mixed local formatting and UTC storage inconsistently
  - Mock layers use different patterns than real backend APIs
- **Performance monitoring timing inconsistency:**
  - Uses `Date.now()` for metrics while rest of app moves to ISO strings
  - Could cause correlation issues in debugging and log analysis
- **Cache TTL and timing drift:**
  - Mixed usage of `Date.now()` for cache expiration vs ISO strings for business logic
  - Potential for subtle timing bugs in cache invalidation
- **Extensive front-end timestamp sprawl:**
  - Over 25 occurrences of date/time handling in mock APIs and UI layers
  - Creates maintenance burden and potential inconsistency with backend standards

Impact: inconsistent audits, brittle day-range queries, confusion/regression risk when moving to a strict validation regime (Zod), backups/exports, cross-platform behavior, bundle bloat, potential cache coherency issues, and increased maintenance complexity.

---

## 3) Standardization decision (no DB migrations)

- Persisted timestamp fields: always set by the application as ISO 8601 UTC strings with milliseconds and Z.
  - Continue to accept existing historical rows that may contain space-formatted timestamps.
- Durations: keep as numeric seconds (as today in timer APIs) and compute in application code.
- Do not alter DB schema or defaults. Treat `DEFAULT CURRENT_TIMESTAMP` as a fallback that app never relies on for new writes.
- Querying by date: prefer explicit UTC or explicit local day ranges computed in application code and passed as parameters; avoid mixing `date(start_time)` with `date('now','localtime')`.
- Validation: at boundaries (IPC, API), validate timestamps with Zod `z.string().datetime()` where feasible.

---

## 4) File-by-file change checklist (app-level only)

The following changes make timestamp handling uniform without schema changes.

### A) `electron/main/api/recent-items-api.ts`
- Replace DB-driven `CURRENT_TIMESTAMP` with app-driven ISO.
- Edits:
  - `addRecentNote`: when existing, `UPDATE recent_items SET viewed_at = ?` with `nowIso()`; when inserting, `INSERT ... VALUES (?, ?)` with `nowIso()`.
  - `addRecentBook`: same adjustments as above.
- Rationale: unifies with ISO standard; keeps ordering by `viewed_at` intact.

Current (DB-driven):
```54:66:electron/main/api/recent-items-api.ts
if (existing) {
  dbRun('UPDATE recent_items SET viewed_at = CURRENT_TIMESTAMP WHERE note_id = ?', [noteId]);
} else {
  dbRun('INSERT INTO recent_items (note_id, viewed_at) VALUES (?, CURRENT_TIMESTAMP)', [noteId]);
}
```

### B) `electron/main/api/settings-api.ts`
- Avoid schema mismatch for `theme_settings.created_at`. Do not insert into `created_at` (column doesn’t exist in schema). Set `updated_at` via ISO.
- Edits:
  - `setSetting`: replace `updated_at = CURRENT_TIMESTAMP` with an ISO parameter.
  - `createTheme`: remove `created_at` from the `INSERT` column list and use ISO for `updated_at`.
  - `setActiveTheme`: replace `updated_at = CURRENT_TIMESTAMP` with ISO.

Current (mismatch and DB-driven times):
```149:156:electron/main/api/settings-api.ts
INSERT INTO theme_settings (theme_name, is_active, created_at, updated_at) VALUES (?, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
```

### C) `electron/main/api/timer-api.ts`
- End/completion flows should be app-driven:
  - `endUserSession`, `endTimerSession`: compute `endTimeIso = nowIso()` and `durationSec = round((end - start)/1000)` in app; set `updated_at` to ISO.
  - `completePomodoroInSession`: same approach for cycles; increment session’s `pomodoro_cycles_completed` and set `updated_at` to ISO.
  - `syncAllSessionPomodoroCounts`: set `updated_at` via ISO.
- Date queries should avoid mixed UTC/local semantics:
  - `getTodayTimerSessions`: pass `[startOfLocalDayIso, endOfLocalDayIso)` as parameters and query `start_time >= ? AND start_time < ?`.
  - `getTimerSessionsByDateRange`/`getTimerStatsByDateRange`: accept `YYYY-MM-DD` local dates, compute ISO range in app, and query with `BETWEEN ? AND ?` on `start_time`.

Current (DB-driven):
```106:115:electron/main/api/timer-api.ts
end_time = CURRENT_TIMESTAMP,
duration = CAST((julianday(CURRENT_TIMESTAMP) - julianday(start_time)) * 86400 AS INTEGER),
updated_at = CURRENT_TIMESTAMP
```
```506:510:electron/main/api/timer-api.ts
... WHERE date(start_time) = date('now', 'localtime') ...
```

### D) `electron/main/database/database-api.ts`
- Already app-driven ISO for notes/folders/books. No functional change required.
- Optional hardening: ensure any place that sets `last_viewed_at` does so via `nowIso()` for uniformity (already done in updates).

### E) `electron/main/api/media-api.ts`
- Already app-driven ISO for `created_at`. Keep as-is. `Date.now()` for filenames is fine (not persisted).

### F) `electron/main/database/database.ts`
- No schema changes (by design of this plan). DB defaults remain but should be unused for new writes.

### G) `electron/main/database/database-hooks.ts`
- No change required. Internal `Date` and `.toISOString()` usage is consistent with the standard.

### H) Centralize time utilities (new module)
- Add `electron/utils/time.ts` with:
  - `nowIso(): string` — `new Date().toISOString()`
  - `toIso(date: Date | number | string): string`
  - `startOfLocalDayIso(date: Date): string`
  - `endOfLocalDayIso(date: Date): string`
  - `secondsBetween(startIso: string, endIso?: string): number`
- Use these helpers in all edits above.

### I) `package.json` and dependency management
- **Decision required:** Either utilize `date-fns` in the new `electron/utils/time.ts` module OR remove the dependency entirely to avoid dead weight (~100KB).
- **Recommendation:** Use `date-fns` functions like `startOfDay`, `endOfDay` for local day boundary calculations in the utility module, or remove if native Date methods suffice.
- **Implementation:** If keeping date-fns, import specific functions in time.ts:
  ```typescript
  import { startOfDay, endOfDay } from 'date-fns';
  ```

### J) Front-end layer acknowledgment
- **No changes required** for front-end mock layers (`src/types/mock-api.ts`), stores (`src/stores/`), and UI formatting - these intentionally remain local-aware and ephemeral.
- **Note:** This creates acceptable temporary inconsistency between backend (UTC ISO) and frontend (local formatting) that should be resolved in future UI standardization effort.
- **Rationale:** Front-end patterns are out of scope for this backend-focused standardization plan.

---

## 5) Validation strategy (Zod)

- At IPC/API boundaries, validate timestamps as ISO:
  - `z.string().datetime()` for fields like `created_at`, `updated_at`, `start_time`, `end_time`, `last_viewed_at`.
- Be tolerant of legacy records formatted as `YYYY-MM-DD HH:MM:SS` by transforming on read where necessary (or loosening Zod parse layer for reads only). Books schema already acknowledges both formats:
```33:43:electron/main/api/books-api/book.schemas.ts
created_at: z
  .string()
  .refine(... 'ISO 8601 (with Z or offset) or SQLite timestamp')
```

---

## 6) Edge cases and pitfalls to avoid

- Mixing UTC/local in SQL: do not compare `date(start_time)` (UTC) with `date('now','localtime')` (local). Compute local day bounds in app and pass as parameters.
- Rounding durations: DB `julianday()` truncates decimals differently than JS. Be explicit and consistent in JS (e.g., `Math.round((end-start)/1000)`).
- Theme settings schema mismatch: do not write `created_at` unless the column exists. This plan avoids migrations by not writing that column.
- Historical data formats: existing rows may have both ISO and space-formatted timestamps. App read paths should parse both.
- **Bootstrap timestamp pattern isolation:** The "Books" folder creation using `CURRENT_TIMESTAMP` in `database.ts:445` is acceptable as bootstrap/setup code, but this pattern should not spread to runtime business logic.
- **Performance correlation complexity:** Mixed timing sources (`Date.now()` vs `new Date().toISOString()`) across performance monitoring and business logic could complicate debugging and log correlation.
- **Mock layer divergence during testing:** Front-end mock APIs use different timestamp patterns than real backend - ensure test consistency doesn't break during standardization implementation.
- **Dependency weight vs utility trade-off:** Unused `date-fns` (~100KB) should be removed or properly utilized to avoid shipping dead code in production builds.
- **Cache timing precision:** Mixed usage of `Date.now()` (millisecond precision) vs ISO string conversions could cause subtle cache invalidation bugs.

---

## 7) Acceptance criteria

- All writes of persisted timestamps originate from app code as ISO 8601 UTC strings.
- No remaining usage of `CURRENT_TIMESTAMP` in `electron/main/api/*.ts` for persisted timestamps (OK to keep in schema defaults).
- Timer end/completion and cycles compute `end_time`, `duration`, and `updated_at` in app code.
- Date-range queries accept local dates, convert to ISO ranges in app, and avoid `localtime` in SQL.
- Settings/theme/recent-items timestamps are ISO-driven.
- No DB migrations performed; schema remains intact.

---

## 8) Quick verification checklist (post-implementation)

- **Comprehensive CURRENT_TIMESTAMP elimination:** Grep shows no `CURRENT_TIMESTAMP` in `electron/main/api/**/*.ts`:
  - Expected remaining hits only in `electron/main/database/database.ts` (schema) and possibly in legacy comments/doc files.
  - Verify removal from ALL timer API completion flows:
    - Lines 286-289: `completePomodoroInSession`
    - Lines 300-301: Pomodoro cycle completion
    - Lines 400-403: `endTimerSession`
    - Lines 717-719: `syncAllSessionPomodoroCounts`
- **SQL date function cleanup:** Confirm elimination of `date('now','localtime')` mixing in timer queries:
  - `getTodayTimerSessions` uses parameterized ISO ranges instead
- Unit/integration checks on timer:
  - Start/end sessions across local midnight boundaries behave as expected with local-day queries.
  - Durations match expectations when ended via app code (not DB julianday calculation).
- Settings and theme updates reflect ISO strings in DB rows.
- Recent items: `viewed_at` rows contain ISO strings.
- **Theme schema compliance:** Verify theme creation no longer attempts to insert into non-existent `created_at` column.
- **Dependency verification:** Confirm `date-fns` is either properly utilized in `electron/utils/time.ts` OR removed from `package.json` to eliminate bundle bloat.
- **Bootstrap pattern isolation:** Verify that `CURRENT_TIMESTAMP` usage in `electron/main/database/database.ts:445` remains isolated to setup code only.
- **Central utility adoption:** Confirm all modified APIs import and use functions from `electron/utils/time.ts` instead of inline `new Date().toISOString()`.
- **Front-end consistency acknowledgment:** Document that front-end layers (`src/types/mock-api.ts`, `src/stores/`, UI formatting) intentionally retain local formatting patterns (no immediate action required).

---

## 9) Implementation priority and comprehensive findings summary

### **Critical (must address immediately):**
- **28 CURRENT_TIMESTAMP eliminations** across recent-items, settings, and timer APIs (sections A, B, C)
- **Timer date query standardization** to avoid UTC/local timezone mixing (section C)
- **Theme schema mismatch fix** - stop writing to non-existent created_at column (section B)
- **Central time utility creation** - establish `electron/utils/time.ts` (section H)

### **Important (should address in implementation phase):**
- **date-fns dependency decision** - utilize or remove to avoid 100KB bundle bloat (section I)
- **Comprehensive verification testing** - implement all checklist items (section 8)
- **Bootstrap pattern isolation** - ensure CURRENT_TIMESTAMP remains limited to setup code

### **Optional (future consideration):**
- **Front-end timestamp pattern alignment** - standardize mock APIs and UI formatting (section J notes)
- **Performance monitoring timing standardization** - align with backend ISO patterns
- **Cache timing precision improvements** - resolve mixed Date.now() vs ISO usage

### **Comprehensive pattern inventory:**
This analysis discovered **118+ timestamp/date occurrences** across the entire codebase, categorized as:
1. **App-driven ISO strings (66 occurrences)** - mostly backend persistence
2. **DB-driven CURRENT_TIMESTAMP (28 occurrences)** - requires elimination
3. **Mixed SQL date functions (8 occurrences)** - timezone issues to fix
4. **Ephemeral Date.now() (16 occurrences)** - acceptable for non-persisted uses
5. **Locale formatting (9 occurrences)** - UI-only, acceptable
6. **Unused date library (1 occurrence)** - decision required
7. **Front-end sprawl (25+ occurrences)** - out of current scope

**Risk assessment:** The **28+ inconsistent CURRENT_TIMESTAMP patterns** pose the highest risk for audit trails, cross-timezone functionality, and Zod validation migration. Timer APIs have the most complex timestamp logic requiring careful refactoring.

---

## 10) Appendix: Additional citations

Schema defaults and indices (not changed in this plan):
```209:224:electron/main/database/database.ts
CREATE TABLE IF NOT EXISTS timer_sessions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  start_time TIMESTAMP NOT NULL,
  end_time TIMESTAMP,
  duration INTEGER,
  session_type TEXT,
  is_completed BOOLEAN DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  ...
  updated_at TIMESTAMP,
  ...
)
```
```391:401:electron/main/database/database.ts
CREATE INDEX IF NOT EXISTS idx_timer_sessions_start_time ON timer_sessions (start_time DESC)
...
CREATE INDEX IF NOT EXISTS idx_notes_updated_at ON notes(updated_at)
CREATE INDEX IF NOT EXISTS idx_folders_updated_at ON folders(updated_at)
```

Timer settings ISO writes (already aligned with plan):
```621:646:electron/main/api/timer-api.ts
const now = new Date().toISOString();
...
fieldsToUpdate.push('updated_at = ?');
params.push(now, currentSettings.id);
```

Books lists using created/updated ordering (works with ISO):
```602:611:electron/main/database/database-api.ts
const query = 'SELECT * FROM books ORDER BY created_at DESC';
```

Media ordering by `created_at` (works with ISO):
```148:158:electron/main/api/media-api.ts
const query = 'SELECT * FROM media_files WHERE book_id = ? ORDER BY created_at DESC';
```











## Section 1: Goals (Detailed Breakdown)

This section outlines the high-level objectives. Follow these as guiding principles throughout implementation.

1. **Step 1: Adopt a Single Canonical Representation for Persisted Timestamps**
   - Use ISO 8601 UTC strings with milliseconds and a trailing Z (e.g., `2025-01-15T10:20:30.123Z`).
   - Action: In all app code that writes to the database, replace any other formats with this standard.
   - Rationale: Ensures consistency across all persisted data.

2. **Step 2: Handle Duration Fields**
   - Keep durations as numeric values (in seconds, to match existing timer code).
   - Action: Compute durations in app code using JavaScript (e.g., `Math.round((endDate.getTime() - startDate.getTime()) / 1000)`).
   - Rationale: Avoids DB-side computations that could introduce inconsistencies.

3. **Step 3: Avoid Reliance on SQLite `CURRENT_TIMESTAMP`**
   - Set all timestamps in application code, not via DB defaults.
   - Action: Identify and replace all instances where `CURRENT_TIMESTAMP` is used in inserts/updates.
   - Rationale: Makes timestamps deterministic and app-controlled.

4. **Step 4: Avoid Database Migrations**
   - Leave existing schema defaults (e.g., `DEFAULT CURRENT_TIMESTAMP`) in place but ignore them for new data.
   - Action: Ensure all writes explicitly provide ISO values, bypassing defaults.
   - Rationale: Minimizes risk and effort.

5. **Step 5: Make Date-Based Queries Deterministic**
   - Ensure queries are timezone-explicit (e.g., compute ranges in app code).
   - Action: Replace mixed timezone queries with parameterized ISO ranges.
   - Rationale: Prevents edge cases like day boundary shifts.

***

## Section 2: Current Timestamp and Date Usage (Verifiable Inventory)

This section catalogs all existing patterns with citations. Use this as a reference to audit your codebase before changes. For each subsection, follow these steps:
- Locate the cited lines in your code editor.
- Note the current behavior (e.g., format used).
- Cross-reference with later change sections for fixes.

### Subsection 2.1: App-Side ISO 8601 Timestamps via `new Date().toISOString()`

1. **Step 1: Review Notes Create/Update and `last_viewed_at` Usage**
   - Citation: Lines 160-176 and 255-305 in `electron/main/database/database-api.ts`.
   - Description: App code sets `created_at`, `updated_at`, and `last_viewed_at` using `const now = new Date().toISOString();` in INSERT/UPDATE queries.
   - Action: Verify this is already consistent with the goal— no immediate change needed here.

2. **Step 2: Review Folders and Books Creation/Updates**
   - Citations: Lines 341-349 and 571-586 in `electron/main/database/database-api.ts`.
   - Description: Uses `const now = new Date().toISOString();` for `created_at` and `updated_at` in INSERT queries.
   - Action: Confirm consistency; harden by centralizing to a utility function (see Section 4H).

3. **Step 3: Review Media Files `created_at`**
   - Citation: Lines 104-112 in `electron/main/api/media-api.ts`.
   - Description: Inserts `new Date().toISOString()` directly into VALUES for `created_at`.
   - Action: Keep as-is; it's aligned.

4. **Step 4: Review Timer Starts and Settings**
   - Citations: Lines 79-88 and 582-598 in `electron/main/api/timer-api.ts`.
   - Description: Uses ISO for `start_time`, `created_at`, and `updated_at` in INSERT queries.
   - Action: Verify; align any related computations (e.g., durations) later.

### Subsection 2.2: DB-Side Timestamps via SQLite `CURRENT_TIMESTAMP`

1. **Step 1: Review Schema Defaults**
   - Citations: Lines 108-116 and 146-147 in `electron/main/database/database.ts`.
   - Description: Tables like notes, folders, etc., have `TIMESTAMP DEFAULT CURRENT_TIMESTAMP` (space-formatted `YYYY-MM-DD HH:MM:SS`).
   - Action: Do not change schema; plan to bypass these defaults in app writes.

2. **Step 2: Review Bootstrap Inserts**
   - Citation: Lines 445-447 in `electron/main/database/database.ts`.
   - Description: Uses `CURRENT_TIMESTAMP` directly in SQL for initial folder creation.
   - Action: Isolate as bootstrap-only; do not replicate in runtime code.

3. **Step 3: Review Settings API Usage**
   - Citation: Lines 85-92 in `electron/main/api/settings-api.ts`.
   - Description: UPDATE and INSERT use `CURRENT_TIMESTAMP` for `updated_at`.
   - Action: Flag for replacement in Section 4B.

4. **Step 4: Review Recent Items API Usage**
   - Citations: Lines 56-66 and 88-99 in `electron/main/api/recent-items-api.ts`.
   - Description: UPDATE and INSERT use `CURRENT_TIMESTAMP` for `viewed_at`.
   - Action: Flag for replacement in Section 4A.

5. **Step 5: Review Timer Completion Computations**
   - Citations: Lines 106-115, 284-303, 286-289, 300-301, 400-403, and 717-719 in `electron/main/api/timer-api.ts`.
   - Description: Uses `CURRENT_TIMESTAMP` for `end_time`, `updated_at`, and julianday for durations.
   - Action: Flag for app-side computation in Section 4C.

6. **Step 6: Review Schema Migration Usage**
   - Citation: Lines 370-372 in `electron/main/database/database.ts`.
   - Description: Adds `created_at` with `DEFAULT CURRENT_TIMESTAMP` in ALTER TABLE.
   - Action: No change; treat as fallback.

### Subsection 2.3: Date-Based Querying in SQL (Mixed Timezone Semantics)

1. **Step 1: Review Date Range Filters**
   - Citation: Lines 475-487 in `electron/main/api/timer-api.ts`.
   - Description: Uses `date(ts.start_time) BETWEEN date(?) AND date(?)` (UTC semantics).
   - Action: Flag for explicit ranges.

2. **Step 2: Review "Today" Queries**
   - Citation: Lines 506-510 in `electron/main/api/timer-api.ts`.
   - Description: Mixes `date(start_time)` (UTC) with `date('now', 'localtime')` (local).
   - Action: Flag for app-computed local ranges in Section 4C.

3. **Step 3: Review Statistics Queries**
   - Citation: Lines 523-542 in `electron/main/api/timer-api.ts`.
   - Description: Similar mixed use of `date(start_time) BETWEEN date(?) AND date(?)`.
   - Action: Standardize to ISO ranges.

### Subsection 2.4: Other Usage Patterns

1. **Step 1: Review Hooks Usage**
   - Citations: Lines 75-83 and 117-123 in `electron/main/database/database-hooks.ts`.
   - Description: Generates `new Date()` and serializes to ISO for events/deletions.
   - Action: Keep as-is; consistent.

2. **Step 2: Review Front-End and Mock Layer Usage**
   - Citation: Various lines in `src/types/mock-api.ts`, etc. (e.g., 21 instances of `new Date().toISOString()`).
   - Description: Extensive use of ISO, `Date.now()`, and local formatting in mocks/UI.
   - Action: Acknowledge as out-of-scope; no changes (see Section 4J).

3. **Step 3: Review Other Patterns (State, Performance, etc.)**
   - Citations: Various files like `src/stores/notesViewStore.ts`, `src/utils/pageLoadMonitor.ts`, etc.
   - Description: Uses `Date.now()` for metrics, local strings for UI, etc.—not persisted.
   - Action: Leave as ephemeral; optional future alignment.

4. **Step 4: Review Unused Dependency**
   - Citation: Line 88 in `package.json`.
   - Description: `date-fns` installed but unused.
   - Action: Decide in Section 4I.

### Subsection 2.5: Type Declarations

1. **Step 1: Review Timer Interfaces**
   - Citation: Lines 186-196 in `src/types/electron-api.d.ts`.
   - Description: Expects ISO strings for timestamps, numbers for durations.
   - Action: Align all code to match this expectation.

***

## Section 3: What’s Inconsistent and Why It Matters (Detailed Analysis)

This section explains problems. For each inconsistency, follow these steps to understand and prepare fixes:
- Identify the issue in your code.
- Note the impact.
- Link to fix in Section 4.

1. **Step 1: Analyze Mixed Time Sources**
   - Issue: App uses ISO, DB uses `CURRENT_TIMESTAMP` (different formats, potential ms/timezone drift).
   - Impact: Inconsistent audits, brittle queries.
   - Fix Prep: Replace all DB sources with app ISO.

2. **Step 2: Analyze Mixed Storage Formats**
   - Issue: ISO vs space-formatted strings.
   - Impact: Parsing errors in validation.

3. **Step 3: Analyze Mixed Timezone Semantics**
   - Issue: UTC vs local in queries.
   - Impact: Edge-case shifts.

4. **Step 4: Analyze Duration Computations**
   - Issue: DB julianday vs app.
   - Impact: Inconsistent calculations.

5. **Step 5: Analyze Schema/API Friction**
   - Issue: `theme_settings` lacks `created_at` but code tries to insert it.
   - Impact: Errors on insert.

6. **Step 6: Analyze Other Inconsistencies (Bootstrap, Dependencies, Front-End, etc.)**
   - Issues: Bootstrap patterns, unused `date-fns`, front-end sprawl, etc.
   - Impact: Maintenance burden, bloat, bugs.
   - Overall Impact: Risks in backups, cross-platform, Zod validation.

***

## Section 4: Standardization Decision (No DB Migrations)

Implement these decisions across all changes.

1. **Step 1: Standardize Persisted Timestamps**
   - Always set as app-provided ISO UTC strings.
   - Accept legacy formats on read.

2. **Step 2: Standardize Durations**
   - Compute in app as seconds.

3. **Step 3: Handle Schema Defaults**
   - Ignore for new writes.

4. **Step 4: Standardize Querying**
   - Compute ranges in app, use parameters.

5. **Step 5: Add Validation**
   - Use Zod `z.string().datetime()` at boundaries.

***

## Section 5: File-by-File Change Checklist (App-Level Only)

This is the core implementation phase. For each file/subsection, follow the numbered steps to make changes. Test after each file.

### Subsection 5A: Changes to `electron/main/api/recent-items-api.ts`

1. **Step 1: Replace `CURRENT_TIMESTAMP` in `addRecentNote`**
   - Locate lines 54-66.
   - Change UPDATE to `SET viewed_at = ?` with `nowIso()` (from utility).
   - Change INSERT to `VALUES (?, ?)` with noteId and `nowIso()`.

2. **Step 2: Apply Same to `addRecentBook`**
   - Mirror Step 1 for book-related code.
   - Rationale: Unifies with ISO; preserves ordering.

3. **Step 3: Test**
   - Add a note/book, verify `viewed_at` is ISO in DB.

### Subsection 5B: Changes to `electron/main/api/settings-api.ts`

1. **Step 1: Update `setSetting`**
   - Replace `updated_at = CURRENT_TIMESTAMP` with `updated_at = ?` and pass `nowIso()`.

2. **Step 2: Update `createTheme`**
   - Locate lines 149-156.
   - Remove `created_at` from INSERT columns and VALUES.
   - Use `updated_at = ?` with `nowIso()`.

3. **Step 3: Update `setActiveTheme`**
   - Replace `updated_at = CURRENT_TIMESTAMP` with ISO parameter.

4. **Step 4: Test**
   - Create/update settings/theme, verify no errors and ISO in DB.

### Subsection 5C: Changes to `electron/main/api/timer-api.ts`

1. **Step 1: Handle End/Completion Flows**
   - For `endUserSession`/`endTimerSession` (lines 106-115, 400-403): Compute `endTimeIso = nowIso()`, `durationSec = secondsBetween(startIso, endTimeIso)`, set `updated_at = nowIso()`.
   - For `completePomodoroInSession` (lines 284-303, 286-289, 300-301): Same for cycles; increment `pomodoro_cycles_completed`, set `updated_at = nowIso()`.
   - For `syncAllSessionPomodoroCounts` (lines 717-719): Set `updated_at = nowIso()`.

2. **Step 2: Standardize Date Queries**
   - For `getTodayTimerSessions` (lines 506-510): Compute `startOfLocalDayIso(new Date())` and `endOfLocalDayIso(new Date())`, query `start_time >= ? AND start_time < ?`.
   - For `getTimerSessionsByDateRange`/`getTimerStatsByDateRange` (lines 475-487, 523-542): Accept local YYYY-MM-DD, compute ISO range, use `BETWEEN ? AND ?`.

3. **Step 3: Test**
   - Start/end sessions across timezones/midnight; verify durations and queries.

### Subsection 5D: `electron/main/database/database-api.ts`

1. **Step 1: Review Existing ISO Usage**
   - Already uses app ISO for notes/folders/books.
   - Action: No changes; optionally replace inline `new Date().toISOString()` with `nowIso()`.

2. **Step 2: Test**
   - Create/update entities, verify ISO.

### Subsection 5E: `electron/main/api/media-api.ts`

1. **Step 1: Review Existing Usage**
   - Already ISO for `created_at`; `Date.now()` for filenames (non-persisted).
   - Action: Keep as-is.

2. **Step 2: Test**
   - Add media, verify timestamps.

### Subsection 5F: `electron/main/database/database.ts`

1. **Step 1: Confirm No Changes**
   - Schema defaults remain; bootstrap `CURRENT_TIMESTAMP` isolated.
   - Action: None.

### Subsection 5G: `electron/main/database/database-hooks.ts`

1. **Step 1: Confirm No Changes**
   - Internal usage consistent.
   - Action: None.

### Subsection 5H: Centralize Time Utilities (New Module)

1. **Step 1: Create `electron/utils/time.ts`**
   - Add file with exports:
     ```typescript
     export function nowIso(): string {
       return new Date().toISOString();
     }
     export function toIso(date: Date | number | string): string {
       return new Date(date).toISOString();
     }
     export function startOfLocalDayIso(date: Date): string {
       const local = new Date(date);
       local.setHours(0, 0, 0, 0);
       return local.toISOString();
     }
     export function endOfLocalDayIso(date: Date): string {
       const local = new Date(date);
       local.setHours(23, 59, 59, 999);
       return local.toISOString();
     }
     export function secondsBetween(startIso: string, endIso?: string): number {
       const start = new Date(startIso).getTime();
       const end = endIso ? new Date(endIso).getTime() : Date.now();
       return Math.round((end - start) / 1000);
     }
     ```

2. **Step 2: Integrate**
   - Import and use in all modified files (e.g., replace `new Date().toISOString()` with `nowIso()`).

3. **Step 3: Test**
   - Call each function, verify outputs match expectations.

### Subsection 5I: `package.json` and Dependency Management

1. **Step 1: Decide on `date-fns`**
   - Option 1: Utilize—import in `time.ts` (e.g., `import { startOfDay, endOfDay } from 'date-fns';` for local day functions).
   - Option 2: Remove entirely if native Date suffices (delete from `package.json`, run `npm install`).

2. **Step 2: Implement Recommendation**
   - Use `date-fns` for precision in day boundaries, or remove to reduce bloat (~100KB).

3. **Step 3: Test**
   - Build app, check bundle size.

### Subsection 5J: Front-End Layer Acknowledgment

1. **Step 1: Review Scope**
   - No changes to mocks, stores, UI (e.g., local formatting remains).

2. **Step 2: Document**
   - Add a note in code/comments: "Front-end uses local patterns; align in future UI refactor."

***

## Section 6: Validation Strategy (Zod)

1. **Step 1: Add Zod Validation at Boundaries**
   - For timestamps: Use `z.string().datetime()` in IPC/API schemas.

2. **Step 2: Handle Legacy Formats**
   - On reads, transform space-formatted to ISO if needed (or loosen Zod for reads).
   - Reference existing book schema (lines 33-43 in `electron/main/api/books-api/book.schemas.ts`) as a model.

3. **Step 3: Test**
   - Validate sample data; ensure legacy rows parse.

***

## Section 7: Edge Cases and Pitfalls to Avoid

For each, add checks in your implementation:

1. **Step 1: Avoid Mixing UTC/Local in SQL**
   - Compute bounds in app.

2. **Step 2: Handle Duration Rounding**
   - Use `Math.round` in JS consistently.

3. **Step 3: Fix Theme Schema Mismatch**
   - Never write to non-existent `created_at`.

4. **Step 4: Parse Historical Data**
   - Support both formats on read.

5. **Step 5: Isolate Bootstrap Patterns**
   - Keep `CURRENT_TIMESTAMP` only in setup.

6. **Step 6: Address Other Pitfalls**
   - Align performance metrics, mocks, dependencies, cache timing as needed.

***

## Section 8: Acceptance Criteria

Verify post-implementation:

1. All persisted timestamps from app as ISO.
2. No `CURRENT_TIMESTAMP` in API files.
3. App computes timer ends/durations.
4. Queries use app-computed ISO ranges.
5. Settings/recent-items use ISO.
6. No migrations.

***

## Section 9: Quick Verification Checklist (Post-Implementation)

Run this after all changes:

1. **Grep for `CURRENT_TIMESTAMP`**: Only in schema/database.ts.
2. **Check SQL Date Functions**: No `date('now','localtime')`.
3. **Test Timer**: Sessions across boundaries, durations accurate.
4. **Check Settings/Recent/Themes**: ISO in DB, no schema errors.
5. **Verify Dependency**: `date-fns` used or removed.
6. **Isolate Bootstrap**: Limited to setup.
7. **Adopt Utilities**: All APIs use `time.ts`.
8. **Document Front-End**: Add notes.

***

## Section 10: Implementation Priority and Comprehensive Findings Summary

### Critical (Implement First):

1. Eliminate 28 `CURRENT_TIMESTAMP` instances (Sections 5A-5C).
2. Standardize timer queries (Section 5C).
3. Fix theme mismatch (Section 5B).
4. Create `time.ts` (Section 5H).

### Important (Next):

1. Decide on `date-fns` (Section 5I).
2. Run verification (Section 8).
3. Isolate bootstrap.

### Optional (Later):

1. Align front-end.
2. Standardize performance/cache.

### Comprehensive Pattern Inventory and Risk:

- Review the 118+ occurrences as categorized.
- Highest risk: 28 DB patterns—focus here for audits/Zod.

***

## Section 11: Appendix: Additional Citations

1. **Step 1: Review Schema Defaults and Indices**
   - Citations: Lines 209-224, 391-401 in `electron/main/database/database.ts`.
   - Action: No changes; verify indices work with ISO.

2. **Step 2: Review Timer Settings Writes**
   - Citation: Lines 621-646 in `electron/main/api/timer-api.ts`.
   - Action: Already ISO; keep.

3. **Step 3: Review Books/Media Ordering**
   - Citations: Lines 602-611 in `electron/main/database/database-api.ts`, 148-158 in `electron/main/api/media-api.ts`.
   - Action: Confirm ISO compatibility.

