import { z } from 'zod';
import {
  BookSchema,
  CreateBookSchema,
  UpdateBookSchema,
  OpenLibrarySearchResultSchema,
  OpenLibraryWorkDetailsSchema,
  OpenLibraryEditionDetailsSchema,
  OpenLibraryAuthorDetailsSchema,
  OpenLibraryISBNDataSchema,
  OpenLibraryISBNResponseSchema,
  BookSearchResultSchema,
  BookWithNoteCountSchema,
  SearchResultSchema,
  HybridSearchResultSchema
} from './book.schemas';

// Core Book types
export type Book = z.infer<typeof BookSchema>;
export type CreateBookPayload = z.infer<typeof CreateBookSchema>;
export type UpdateBookPayload = z.infer<typeof UpdateBookSchema>;

// OpenLibrary API types
export type OpenLibrarySearchResult = z.infer<typeof OpenLibrarySearchResultSchema>;
export type OpenLibraryWorkDetails = z.infer<typeof OpenLibraryWorkDetailsSchema>;
export type OpenLibraryEditionDetails = z.infer<typeof OpenLibraryEditionDetailsSchema>;
export type OpenLibraryAuthorDetails = z.infer<typeof OpenLibraryAuthorDetailsSchema>;
export type OpenLibraryISBNData = z.infer<typeof OpenLibraryISBNDataSchema>;
export type OpenLibraryISBNResponse = z.infer<typeof OpenLibraryISBNResponseSchema>;

// Enhanced search and display types
export type BookSearchResult = z.infer<typeof BookSearchResultSchema>;
export type BookWithNoteCount = z.infer<typeof BookWithNoteCountSchema>;

// Search result types
export type SearchResult = z.infer<typeof SearchResultSchema>;
export type HybridSearchResult = z.infer<typeof HybridSearchResultSchema>;

// Additional utility types
export type BookStatus = Book['status'];
export type BookId = NonNullable<Book['id']>;
export type BookWithId = Book & { id: BookId };

// Type guards for runtime type checking
export const isBook = (obj: unknown): obj is Book => {
  return BookSchema.safeParse(obj).success;
};

export const isBookSearchResult = (obj: unknown): obj is BookSearchResult => {
  return BookSearchResultSchema.safeParse(obj).success;
};

export const isOpenLibrarySearchResult = (obj: unknown): obj is OpenLibrarySearchResult => {
  return OpenLibrarySearchResultSchema.safeParse(obj).success;
};

// Validation helper functions
export const validateBook = (data: unknown): Book => {
  return BookSchema.parse(data);
};

export const validateCreateBookPayload = (data: unknown): CreateBookPayload => {
  return CreateBookSchema.parse(data);
};

export const validateUpdateBookPayload = (data: unknown): UpdateBookPayload => {
  return UpdateBookSchema.parse(data);
};

export const validateBookSearchResult = (data: unknown): BookSearchResult => {
  return BookSearchResultSchema.parse(data);
};

export const validateOpenLibrarySearchResult = (data: unknown): OpenLibrarySearchResult => {
  return OpenLibrarySearchResultSchema.parse(data);
};

// Safe parsing functions (return success/error objects)
export const safeParseBook = (data: unknown) => {
  return BookSchema.safeParse(data);
};

export const safeParseCreateBookPayload = (data: unknown) => {
  return CreateBookSchema.safeParse(data);
};

export const safeParseUpdateBookPayload = (data: unknown) => {
  return UpdateBookSchema.safeParse(data);
};

export const safeParseBookSearchResult = (data: unknown) => {
  return BookSearchResultSchema.safeParse(data);
};

export const safeParseOpenLibrarySearchResult = (data: unknown) => {
  return OpenLibrarySearchResultSchema.safeParse(data);
};
