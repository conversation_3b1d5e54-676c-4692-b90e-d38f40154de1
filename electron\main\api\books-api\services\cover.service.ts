import { AxiosInstance } from 'axios';
import sharp from 'sharp';
import fs from 'node:fs';
import path from 'node:path';
import httpClient from '../utils/http.client';
import logger from '../utils/logger';

/**
 * Service for handling cover image processing and downloading
 * Provides modern image processing with Sharp, standardized output formats, and comprehensive validation
 */
export class CoverService {
  private readonly httpClient: AxiosInstance;
  private readonly logger: typeof logger;
  private readonly standardizeToWebP: boolean;

  // Image processing configuration
  private readonly MAX_WIDTH = 800;
  private readonly MAX_HEIGHT = 1200;
  private readonly WEBP_QUALITY = 80;
  private readonly WEBP_EFFORT = 4;

  constructor(
    httpClientInstance: AxiosInstance = httpClient,
    loggerInstance: typeof logger = logger,
    options?: { standardizeToWebP?: boolean }
  ) {
    this.httpClient = httpClientInstance;
    this.logger = loggerInstance;
    // Preserve original format by default; allow opting into WebP standardization
    this.standardizeToWebP = options?.standardizeToWebP === true;
  }

  /**
   * Main entry point: downloads and validates an image.
   * If standardization is enabled, resizes and converts to WebP; otherwise, returns original buffer.
   * @param url Cover image URL (http/https, data:image/*, noti-media://)
   * @returns Promise of image buffer (original or standardized)
   */
  async processCoverUrl(url: string): Promise<Buffer> {
    try {
      this.logger.info('Starting cover processing', { url });

      // Download the image
      const imageBuffer = await this.downloadImageBuffer(url);
      
      // Validate the downloaded image
      if (!(await this.validateImageBuffer(imageBuffer))) {
        throw new Error('Downloaded image is invalid or corrupted');
      }

      // Optionally standardize to WebP; otherwise, preserve original format
      const processedBuffer = this.standardizeToWebP
        ? await this.standardizeImage(imageBuffer)
        : imageBuffer;
      
      this.logger.info('Cover processing completed successfully', { 
        url, 
        originalSize: imageBuffer.length,
        processedSize: processedBuffer.length,
        compressionRatio: Math.round((1 - processedBuffer.length / imageBuffer.length) * 100)
      });

      return processedBuffer;

    } catch (error) {
      this.logger.error('Error processing cover image', { url, error });
      throw new Error(`Failed to process cover image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Downloads image from multiple sources: http(s), data:image/*, noti-media://
   * @param url Image source URL
   * @returns Promise of raw image buffer
   */
  async downloadImageBuffer(url: string): Promise<Buffer> {
    try {
      this.logger.debug('Downloading image', { url });
      
      // data URLs
      if (/^data:image\//i.test(url)) {
        const commaIndex = url.indexOf(',');
        if (commaIndex === -1) throw new Error('Malformed data URL');
        const meta = url.substring(5, commaIndex); // after 'data:'
        const isBase64 = /;base64/i.test(meta);
        const payload = url.substring(commaIndex + 1);
        const buffer = isBase64 ? Buffer.from(payload, 'base64') : Buffer.from(decodeURIComponent(payload), 'utf-8');
        this.logger.debug('Parsed image from data URL', { size: buffer.length });
        return buffer;
      }

      // noti-media:// URLs
      if (/^noti-media:\/\//i.test(url)) {
        // Strip scheme
        let filePath = url.replace(/^noti-media:\/\//i, '');
        // On Windows, incoming path may start with a leading '/'
        if (process.platform === 'win32' && filePath.startsWith('/')) {
          filePath = filePath.substring(1);
        }
        // Decode each segment
        const decoded = filePath
          .split('/')
          .map((p) => {
            try { return decodeURIComponent(p); } catch { return p; }
          })
          .join('/');
        const normalized = process.platform === 'win32' ? decoded.replace(/\//g, '\\') : decoded;
        if (!fs.existsSync(normalized)) {
          throw new Error(`File not found for noti-media URL: ${normalized}`);
        }
        // Pre-read size check to avoid loading very large files into memory
        const maxSize = 10 * 1024 * 1024; // 10MB
        const stats = fs.statSync(normalized);
        if (stats.size > maxSize) {
          this.logger.warn('Local noti-media image exceeds size limit', { path: normalized, size: stats.size, maxSize });
          throw new Error('Image exceeds 10MB size limit');
        }

        const buffer = fs.readFileSync(normalized);
        this.logger.debug('Read image from noti-media file', { path: normalized, size: buffer.length });
        return buffer;
      }

      // http(s)
      const response = await this.httpClient.get(url, {
        responseType: 'arraybuffer',
        headers: { 'Accept': 'image/*,*/*;q=0.8' },
        maxRedirects: 5
      });
      if (!response.data || response.data.byteLength === 0) throw new Error('Empty response received');
      const buffer = Buffer.from(response.data);
      this.logger.debug('Image downloaded successfully', { url, size: buffer.length, contentType: response.headers['content-type'] });
      return buffer;

    } catch (error) {
      this.logger.error('Error downloading image', { url, error });
      throw new Error(`Failed to download image: ${error instanceof Error ? error.message : 'Network error'}`);
    }
  }

  /**
   * Validates image format, size, and integrity using comprehensive checks
   * NOTE: 10MB maximum size per project policy
   * @param buffer Image buffer to validate
   * @returns True if image is valid
   */
  async validateImageBuffer(buffer: Buffer): Promise<boolean> {
    try {
      // Check if buffer is not empty
      if (!buffer || buffer.length === 0) {
        this.logger.warn('Image buffer is empty');
        return false;
      }

      // Check minimum size (at least 100 bytes for a valid image)
      if (buffer.length < 100) {
        this.logger.warn('Image buffer too small', { size: buffer.length });
        return false;
      }

      // Check maximum size (10MB limit)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (buffer.length > maxSize) {
        this.logger.warn('Image buffer too large', { size: buffer.length, maxSize });
        return false;
      }

      // Check for common image format signatures
      const isValidFormat = this.isValidImageFormat(buffer);
      if (!isValidFormat) {
        this.logger.warn('Invalid image format detected');
        return false;
      }

      // Additional validation: try to read metadata with Sharp to detect corruption
      try {
        const metadata = await sharp(buffer).metadata();
        this.logger.debug('Image metadata validation passed', {
          size: buffer.length,
          width: metadata.width,
          height: metadata.height,
          format: metadata.format
        });
      } catch (sharpError) {
        this.logger.warn('Image appears corrupted - Sharp cannot read metadata', {
          size: buffer.length,
          sharpError: sharpError instanceof Error ? sharpError.message : 'Unknown Sharp error'
        });
        return false;
      }

      this.logger.debug('Comprehensive image buffer validation passed', { size: buffer.length });
      return true;

    } catch (error) {
      this.logger.error('Error validating image buffer', { error });
      return false;
    }
  }

  /**
   * Processes image with Sharp: resize, convert to WebP, optimize quality
   * @param buffer Raw image buffer to process
   * @returns Promise of processed WebP buffer
   */
  async standardizeImage(buffer: Buffer): Promise<Buffer> {
    try {
      this.logger.debug('Starting image standardization with Sharp');

      // Get image metadata first
      const metadata = await sharp(buffer).metadata();
      this.logger.debug('Image metadata', {
        format: metadata.format,
        width: metadata.width,
        height: metadata.height,
        channels: metadata.channels,
        hasAlpha: metadata.hasAlpha
      });

      // Process with Sharp pipeline
      const processedBuffer = await this.processWithSharp(buffer);

      this.logger.debug('Image standardization completed', {
        originalFormat: metadata.format,
        originalSize: buffer.length,
        processedSize: processedBuffer.length
      });

      return processedBuffer;

    } catch (error) {
      this.logger.error('Error standardizing image with Sharp', { error });
      throw new Error(`Image processing failed: ${error instanceof Error ? error.message : 'Sharp processing error'}`);
    }
  }

  /**
   * Generates OpenLibrary cover URLs with validation
   * @param coverId OpenLibrary cover ID
   * @param size Cover size (Small, Medium, Large)
   * @returns Cover URL string
   */
  getOpenLibraryCoverUrl(coverId: number, size: 'S' | 'M' | 'L' = 'L'): string {
    if (!coverId || coverId <= 0) {
      throw new Error('Invalid cover ID provided');
    }

    if (!['S', 'M', 'L'].includes(size)) {
      throw new Error('Invalid size parameter. Must be S, M, or L');
    }

    const url = `https://covers.openlibrary.org/b/id/${coverId}-${size}.jpg`;
    this.logger.debug('Generated OpenLibrary cover URL', { coverId, size, url });
    
    return url;
  }

  /**
   * Internal Sharp processing pipeline
   * @param buffer Image buffer to process with Sharp
   * @returns Promise of processed buffer
   */
  private async processWithSharp(buffer: Buffer): Promise<Buffer> {
    return sharp(buffer)
      // Resize with aspect ratio preservation
      .resize(this.MAX_WIDTH, this.MAX_HEIGHT, {
        fit: 'inside',
        withoutEnlargement: true
      })
      // Convert to WebP with optimized settings
      .webp({
        quality: this.WEBP_QUALITY,
        effort: this.WEBP_EFFORT
      })
      // Return as buffer
      .toBuffer();
  }

  /**
   * Check if buffer contains a valid image format
   * @param buffer Image buffer to check
   * @returns True if format is recognized
   */
  private isValidImageFormat(buffer: Buffer): boolean {
    if (!buffer || buffer.length < 4) return false;
    const b = buffer;
    // JPEG
    if (b[0] === 0xFF && b[1] === 0xD8 && b[2] === 0xFF) return true;
    // PNG
    if (b[0] === 0x89 && b[1] === 0x50 && b[2] === 0x4E && b[3] === 0x47) return true;
    // GIF
    if (b[0] === 0x47 && b[1] === 0x49 && b[2] === 0x46) return true;
    // WebP: RIFF....WEBP
    if (
      b.length >= 12 &&
      b[0] === 0x52 && b[1] === 0x49 && b[2] === 0x46 && b[3] === 0x46 &&
      b[8] === 0x57 && b[9] === 0x45 && b[10] === 0x42 && b[11] === 0x50
    ) return true;
    // BMP
    if (b[0] === 0x42 && b[1] === 0x4D) return true;
    // TIFF (little endian)
    if (b[0] === 0x49 && b[1] === 0x49 && b[2] === 0x2A && b[3] === 0x00) return true;
    // TIFF (big endian)
    if (b[0] === 0x4D && b[1] === 0x4D && b[2] === 0x00 && b[3] === 0x2A) return true;
    return false;
  }
}
