// Type declarations for Electron IPC communication

// Note related interfaces
export interface Note {
  id?: number;
  title: string;
  content?: string;
  html_content?: string; // Remove null as an option to fix compatibility
  folder_id?: number | null;
  book_id?: number | null;
  created_at?: string;
  updated_at?: string;
}

export interface Folder {
  id: number; // Make id required to match with existing code
  name: string;
  parent_id?: number | null;
  book_id?: number | null;
  color?: string | null;
  order?: number | null;
  created_at?: string;
  updated_at?: string;
}

// Enhanced folder with additional properties for hierarchy
export interface FolderWithMeta extends Folder {
  notesCount?: number;
  childFoldersCount?: number;
  children?: FolderWithMeta[];
}

// API interfaces
export interface NotesAPI {
  create: (note: Note) => Promise<Note>;
  createForBook: (bookId: number, customTitle?: string, folderId?: number | null) => Promise<Note>;
  getAll: (options?: any) => Promise<Note[]>;
  getById: (id: number) => Promise<Note>;
  update: (id: number, note: Partial<Note>) => Promise<Note>;
  delete: (id: number) => Promise<void>;
  getByFolderId: (folderId: number) => Promise<Note[]>;
  export: (id: number, format: string) => Promise<string>;
  exportMultiple: (items: Array<{ id: number; type: 'folder' | 'note'; name: string }>, format: string, options?: { includeSubfolders?: boolean; includeNotes?: boolean }) => Promise<string>;
  import: (content: string, format: string, title: string) => Promise<Note>;
  linkToBook: (noteId: number, bookId: number | null) => Promise<Note>;
  autoLinkInFolder: (folderId: number) => Promise<{ linked: number; errors: number }>;
}

export interface FoldersAPI {
  create: (folder: Omit<Folder, 'id'>) => Promise<Folder>;
  getAll: () => Promise<Folder[]>;
  getById: (id: number) => Promise<Folder>;
  update: (id: number, folder: Partial<Folder>) => Promise<Folder>;
  delete: (id: number, targetFolderId?: number | null) => Promise<void>;
  getChildren: (parentId: number | null) => Promise<Folder[]>;
  getHierarchy: () => Promise<FolderWithMeta[]>;
  getInheritedBookId: (parentId: number | null) => Promise<number | null>;
}

export interface RecentItemsAPI {
  addNote: (noteId: number) => Promise<void>;
  getRecentNotes: (limit?: number) => Promise<Note[]>;
}

// Media file related interfaces
export interface MediaFile {
  id?: number;
  note_id: number | null;
  book_id?: number | null;
  file_path: string;
  file_name: string;
  file_type: string;
  file_size: number;
  is_cover?: boolean;
  created_at?: string;
}

export interface MediaAPI {
  save: (noteId: number | null, fileData: number[], fileName: string, fileType: string) => Promise<MediaFile>;
  getById: (id: number) => Promise<MediaFile>;
  getByNoteId: (noteId: number) => Promise<MediaFile[]>;
  delete: (id: number) => Promise<{ success: boolean; id: number }>;
  updateNote: (id: number, noteId: number | null) => Promise<MediaFile>;
  saveBookCover: (bookId: number, coverData: Uint8Array | number[], fileName?: string) => Promise<MediaFile>;
  getStoragePath: () => Promise<string>;
  getMediaUrl?: (filePath: string) => Promise<string>; // Added this method
}

// Book related interfaces
export interface Book {
  id?: number;
  title: string;
  author?: string | null;
  isbn?: string | null;
  cover_url?: string | null;
  publication_date?: string | null;
  description?: string | null;
  page_count?: number | null;
  current_page?: number | null;
  rating?: number | null;
  language?: string | null;
  genres?: string | null;
  olid?: string | null;
  status?: string | null;
  custom_fields?: string | null;
  created_at?: string;
  updated_at?: string;
}

export interface BookWithNoteCount extends Book {
  notesCount: number;
  recentNote?: {
    id: number;
    title: string;
    last_viewed_at: string;
  };
  addedDate?: Date;
  cover_media_url?: string | null;
  isLoading?: boolean; // For pulsing animation while book data is being fetched
}

export interface BookSearchResult {
  title: string;
  author_name?: string[];
  isbn?: string[];
  cover_i?: number;
  cover_edition_key?: string;
  first_publish_year?: number;
  language?: string[];
  edition_count?: number;
  key?: string;
  subject?: string[];
  publisher?: string[];
  publish_year?: number[];
  oclc?: string[];
  lccn?: string[];
  olid?: string;
  cover_url?: string;
  description?: string;
  genres?: string;
  isbn_primary?: string;
  hasCustomCover?: boolean;
  userModifiedCover?: boolean;
  preventCoverOverride?: boolean;
}

// Language conversion types
export type DisplayLanguage = 'en' | 'es' | 'fr' | 'de' | 'it' | 'pt' | 'ru' | 'zh' | 'ja' | 'ko';

export interface LanguageInfo {
  code: string;
  name: string;
}

export interface BooksAPI {
  create: (book: Partial<Book>, downloadCover?: boolean) => Promise<Book>;
  getAll: () => Promise<Book[]>;
  getAllWithNoteCounts: () => Promise<BookWithNoteCount[]>;
  getBooksWithMetadata: () => Promise<BookWithNoteCount[]>;
  getById: (id: number) => Promise<Book>;
  getByIsbn: (isbn: string) => Promise<Book | null>;
  getByOlid: (olid: string) => Promise<Book | null>;
  update: (id: number, book: Partial<Book>) => Promise<Book>;
  delete: (id: number) => Promise<{ success: boolean; id: number }>;
  getRecent: (days?: number) => Promise<Book[]>;

  // Search operations
  search: (searchTerm: string) => Promise<Book[]>;
  searchOnline: (query: string, limit?: number) => Promise<BookSearchResult[]>;
  searchHybrid: (searchTerm: string, includeOnline?: boolean, onlineLimit?: number) => Promise<{
    localResults: Book[];
    onlineResults: BookSearchResult[];
  }>;

  // OpenLibrary integration
  getDetailsFromOpenLibrary: (olid: string) => Promise<Partial<Book>>;
  addFromOpenLibrary: (searchResult: BookSearchResult) => Promise<Book>;
  downloadCover: (coverUrl: string, filename: string) => Promise<string>;

  // Utility functions
  checkAndDownloadMissingCovers: () => Promise<void>;
  ensureFoldersForAll: () => Promise<{ created: number; errors: number; total: number }>;
  getBooksWithoutFolders: () => Promise<Book[]>;
}

// Timer related interfaces
export interface TimerSession {
  id: number;
  start_time: string; // ISO 8601 format
  end_time?: string | null;
  duration?: number | null; // Duration in seconds
  session_type: string; // e.g., 'work', 'break'
  is_completed: 0 | 1;
  created_at: string;
  updated_at?: string;
  // Frontend-specific fields
  focus?: string | null;
  category?: string | null;
  // User session fields
  session_name?: string | null;
  name?: string | null; // Alias for session_name for compatibility
  is_user_session?: 0 | 1;
  pomodoro_cycles_completed?: number | null;
  actual_pomodoro_count?: number | null; // Computed field from SQL queries
}

export interface PomodoroCycle {
  id: number;
  session_id: number;
  cycle_type: 'pomodoro' | 'short_break' | 'long_break';
  start_time: string;
  end_time?: string | null;
  duration?: number | null;
  completed: 0 | 1;
  created_at: string;
}

export interface TimerStats {
  total_sessions: number;
  total_duration: number | null;
  work_sessions: number;
  work_duration: number | null;
  break_sessions: number;
  break_duration: number | null;
  total_pomodoros: number;
}

export interface TimerSettings {
  id: number;
  work_duration: number; // seconds
  short_break_duration: number; // seconds
  long_break_duration: number; // seconds
  long_break_interval: number; // number of work sessions before long break
  auto_start_breaks: 0 | 1;
  auto_start_work: 0 | 1;
  created_at: string;
  updated_at: string;
}

export interface TimerAPI {
  // Timer sessions
  start: (sessionType?: string, focus?: string, category?: string) => Promise<TimerSession>;
  end: (sessionId: number) => Promise<TimerSession>;
  getSession: (sessionId: number) => Promise<TimerSession>;
  getSessionsByDateRange: (startDate: string, endDate: string) => Promise<TimerSession[]>;
  getTodaySessions: () => Promise<TimerSession[]>;
  getStatsByDateRange: (startDate: string, endDate: string) => Promise<TimerStats>;
  deleteSession: (sessionId: number) => Promise<{ success: boolean; id: number }>;

  // User sessions
  createUserSession: (sessionName: string, description?: string, category?: string) => Promise<TimerSession>;
  getActiveUserSession: () => Promise<TimerSession | null>;
  endUserSession: (sessionId: number) => Promise<TimerSession>;
  updateSession: (sessionId: number, updateData: {
    duration?: number;
    pomodoro_cycles_completed?: number;
    focus?: string;
    category?: string;
    session_name?: string;
  }) => Promise<TimerSession>;

  // Pomodoro cycles
  startPomodoroInSession: (sessionId: number, cycleType: string) => Promise<PomodoroCycle>;
  completePomodoroInSession: (sessionId: number, cycleId: number) => Promise<PomodoroCycle>;
  cancelActiveCycle: (sessionId: number, cycleId: number) => Promise<void>;

  // Timer settings
  getSettings: () => Promise<TimerSettings>;
  updateSettings: (settingsUpdates: Partial<Omit<TimerSettings, 'id' | 'created_at' | 'updated_at'>>) => Promise<TimerSettings>;
  resetSettings: () => Promise<TimerSettings>;

  // Utility functions
  syncAllSessionPomodoroCounts: () => Promise<{ updated: number; total: number }>;
}


// Dialog API for folder selection
export interface DialogAPI {
  selectFolder: () => Promise<string | null>;
}

// Discord API interface
export interface DiscordAPI {
  initialize: () => Promise<boolean>;
  setEnabled: (enabled: boolean) => Promise<boolean>;
  setActivity: (activityData: any) => Promise<boolean>;
  setActiveState: () => Promise<boolean>;
  setIdle: () => Promise<boolean>;
  updateSettings: (settings: any) => Promise<boolean>;
  clearActivity: () => Promise<boolean>;
  getStatus: () => Promise<{ connected: boolean; enabled: boolean; settings: any }>;
  destroy: () => Promise<boolean>;
  testConnection: () => Promise<boolean>;
}

// Settings API interface
export interface SettingsAPI {
  get: (key: string) => Promise<any>;
  getByCategory: (category: string) => Promise<any[]>;
  getAll: () => Promise<any[]>;
  set: (key: string, value: any, category?: string) => Promise<any>;
  delete: (key: string) => Promise<any>;
  getActiveTheme: () => Promise<any>;
  getAllThemes: () => Promise<any[]>;
  createTheme: (themeName: string) => Promise<any>;
  setActiveTheme: (themeId: number) => Promise<any>;
  deleteTheme: (themeId: number) => Promise<any>;
}


// Expose as global window property
declare global {
  interface Window {
    // Electron IPC
    ipcRenderer: import('electron').IpcRenderer;

    // Auto-save timeout for notes
    autoSaveTimeout?: NodeJS.Timeout;

    // Database API
    db: {
      notes: NotesAPI;
      folders: FoldersAPI;
      recentItems: RecentItemsAPI;
      media: MediaAPI;
      books: BooksAPI;
      timer: TimerAPI;
      discord: DiscordAPI;
    };

    // Electron API
    electronAPI: {
      selectFolder: () => Promise<string | null>;
      settings: SettingsAPI;
      /* SYNC SYSTEM DISABLED - Sync API commented out pending backend refactor
      sync: {
        perform: (directory: string) => Promise<any>;
        import: (directory: string) => Promise<any>;
        getStatus: () => Promise<any>;
        configure: (settings: any) => Promise<any>;
        browseDirectory: () => Promise<string | null>;
      };
      */
    };

    // Window controls
    windowControls: {
      minimize: () => Promise<boolean>;
      maximize: () => Promise<boolean>;
      close: () => Promise<boolean>;
    };
  }
}
