import { ipc<PERSON>enderer, contextBridge } from 'electron'

// Import our type-safe API bridge
import { dbApi } from './api-bridge'

// --------- Expose some API to the Renderer process ---------
// Expose our database API first
contextBridge.exposeInMainWorld('db', dbApi);

// Expose sync and dialog APIs (backup system removed, replaced with sync)
contextBridge.exposeInMainWorld('electronAPI', {
  settings: dbApi.settings,
  selectFolder: dbApi.selectFolder
});

// Then expose the ipcRenderer functions
contextBridge.exposeInMainWorld('ipcRenderer', {
  on(...args: Parameters<typeof ipcRenderer.on>) {
    const [channel, listener] = args
    return ipcRenderer.on(channel, (event, ...args) => listener(event, ...args))
  },
  off(...args: Parameters<typeof ipcRenderer.off>) {
    const [channel, ...omit] = args
    return ipcRenderer.off(channel, ...omit)
  },
  send(...args: Parameters<typeof ipcRenderer.send>) {
    const [channel, ...omit] = args
    return ipcRenderer.send(channel, ...omit)
  },
  invoke(...args: Parameters<typeof ipcRenderer.invoke>) {
    const [channel, ...omit] = args
    return ipcRenderer.invoke(channel, ...omit)
  },

  // You can expose other APIs you need here.
  // ...
})

// The following section is commented out because we're already exposing dbApi above
// This was causing the error: Cannot bind an API on top of an existing property on the window object
/*
// Old implementation - REMOVED to fix duplicate contextBridge.exposeInMainWorld('db', ...) error
// Expose database API for use in renderer process - THIS IS DUPLICATED
// contextBridge.exposeInMainWorld('db', {
  // Notes API
  notes: {
    create: (note: any) => ipcRenderer.invoke('notes:create', note),
    getAll: (options?: any) => ipcRenderer.invoke('notes:getAll', options),
    getById: (id: number) => ipcRenderer.invoke('notes:getById', id),
    getByFolderId: (folderId: number) => ipcRenderer.invoke('notes:getByFolderId', folderId),
    getByBookId: (bookId: number) => ipcRenderer.invoke('notes:getByBookId', bookId),
    update: (id: number, noteUpdates: any) => ipcRenderer.invoke('notes:update', id, noteUpdates),
    delete: (id: number) => ipcRenderer.invoke('notes:delete', id),
    search: (searchTerm: string) => ipcRenderer.invoke('notes:search', searchTerm)
  },

  // Folders API
  folders: {
    create: (folder: any) => ipcRenderer.invoke('folders:create', folder),
    getAll: () => ipcRenderer.invoke('folders:getAll'),
    getById: (id: number) => ipcRenderer.invoke('folders:getById', id),
    getChildren: (parentId: number | null) => ipcRenderer.invoke('folders:getChildren', parentId),
    getHierarchy: () => ipcRenderer.invoke('folders:getHierarchy'),
    update: (id: number, folderUpdates: any) => ipcRenderer.invoke('folders:update', id, folderUpdates),
    delete: (id: number, targetFolderId?: number) => ipcRenderer.invoke('folders:delete', id, targetFolderId)
  },

  // Recent Items API
  recentItems: {
    addNote: (noteId: number) => ipcRenderer.invoke('recentItems:addNote', noteId),
    addBook: (bookId: number) => ipcRenderer.invoke('recentItems:addBook', bookId),
    getNotes: (limit?: number) => ipcRenderer.invoke('recentItems:getNotes', limit),
    getBooks: (limit?: number) => ipcRenderer.invoke('recentItems:getBooks', limit),
    getAll: (limit?: number) => ipcRenderer.invoke('recentItems:getAll', limit),
    clearAll: () => ipcRenderer.invoke('recentItems:clearAll'),
    delete: (id: number) => ipcRenderer.invoke('recentItems:delete', id)
  },

  // Settings API
  settings: {
    get: (key: string) => ipcRenderer.invoke('settings:get', key),
    getByCategory: (category: string) => ipcRenderer.invoke('settings:getByCategory', category),
    getAll: () => ipcRenderer.invoke('settings:getAll'),
    set: (key: string, value: any, category?: string) => ipcRenderer.invoke('settings:set', key, value, category),
    delete: (key: string) => ipcRenderer.invoke('settings:delete', key)
  },

  // Themes API
  themes: {
    getActive: () => ipcRenderer.invoke('themes:getActive'),
    getAll: () => ipcRenderer.invoke('themes:getAll'),
    create: (themeName: string) => ipcRenderer.invoke('themes:create', themeName),
    setActive: (themeId: number) => ipcRenderer.invoke('themes:setActive', themeId),
    delete: (themeId: number) => ipcRenderer.invoke('themes:delete', themeId)
  },

  // Timer API
  timer: {
    // Timer sessions
    start: (sessionType?: string, focus?: string, category?: string) =>
      ipcRenderer.invoke('timer:start', sessionType, focus, category),
    end: (sessionId: number) => ipcRenderer.invoke('timer:end', sessionId),
    getSession: (sessionId: number) => ipcRenderer.invoke('timer:getSession', sessionId),
    getSessionsByDateRange: (startDate: string, endDate: string) =>
      ipcRenderer.invoke('timer:getSessionsByDateRange', startDate, endDate),
    getTodaySessions: () => ipcRenderer.invoke('timer:getTodaySessions'),
    getStatsByDateRange: (startDate: string, endDate: string) =>
      ipcRenderer.invoke('timer:getStatsByDateRange', startDate, endDate),
    deleteSession: (sessionId: number) => ipcRenderer.invoke('timer:deleteSession', sessionId),
    updateSession: (sessionId: number, updateData: any) => ipcRenderer.invoke('timer:updateSession', sessionId, updateData),

    // User sessions
    createUserSession: (sessionName: string, focus?: string, category?: string) =>
      ipcRenderer.invoke('timer:createUserSession', sessionName, focus, category),
    getActiveUserSession: () => ipcRenderer.invoke('timer:getActiveUserSession'),
    endUserSession: (sessionId: number) => ipcRenderer.invoke('timer:endUserSession', sessionId),

    // Pomodoro cycles
    startPomodoroInSession: (sessionId: number, cycleType: string) =>
      ipcRenderer.invoke('timer:startPomodoroInSession', sessionId, cycleType),
    completePomodoroInSession: (sessionId: number, cycleId: number) =>
      ipcRenderer.invoke('timer:completePomodoroInSession', sessionId, cycleId),
    cancelActiveCycle: (sessionId: number, cycleId: number) =>
      ipcRenderer.invoke('timer:cancelActiveCycle', sessionId, cycleId),

    // Timer settings
    getSettings: () => ipcRenderer.invoke('timer:getSettings'),
    updateSettings: (settingsUpdates: any) => ipcRenderer.invoke('timer:updateSettings', settingsUpdates),
    resetSettings: () => ipcRenderer.invoke('timer:resetSettings')
  },

// Window controls API
contextBridge.exposeInMainWorld('windowControls', {
  minimize: () => ipcRenderer.invoke('window:minimize'),
  maximize: () => ipcRenderer.invoke('window:maximize'),
  close: () => ipcRenderer.invoke('window:close')
})

// --------- Preload scripts loading ---------
function domReady(condition: DocumentReadyState[] = ['complete', 'interactive']) {
  return new Promise((resolve) => {
    if (condition.includes(document.readyState)) {
      resolve(true)
    } else {
      document.addEventListener('readystatechange', () => {
        if (condition.includes(document.readyState)) {
          resolve(true)
        }
      })
    }
  })
}

/**
 * https://tobiasahlin.com/spinkit
 * https://connoratherton.com/loaders
 * https://projects.lukehaas.me/css-loaders
 * https://matejkustec.github.io/SpinThatShit
 */
function useLoading() {
  const safeDOM = {
    append(parent: HTMLElement, child: HTMLElement) {
      if (!Array.from(parent.children).find(e => e === child)) {
        return parent.appendChild(child)
      }
    },
    remove(parent: HTMLElement, child: HTMLElement) {
      if (Array.from(parent.children).find(e => e === child)) {
        return parent.removeChild(child)
      }
    },
  }

  const className = `loaders-css__square-spin`
  const styleContent = `
@keyframes square-spin {
  25% { transform: perspective(100px) rotateX(180deg) rotateY(0); }
  50% { transform: perspective(100px) rotateX(180deg) rotateY(180deg); }
  75% { transform: perspective(100px) rotateX(0) rotateY(180deg); }
  100% { transform: perspective(100px) rotateX(0) rotateY(0); }
}
.${className} > div {
  animation-fill-mode: both;
  width: 50px;
  height: 50px;
  background: #fff;
  animation: square-spin 3s 0s cubic-bezier(0.09, 0.57, 0.49, 0.9) infinite;
}
.app-loading-wrap {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #282c34;
  z-index: 9;
}
    `
  const oStyle = document.createElement('style')
  const oDiv = document.createElement('div')

  oStyle.id = 'app-loading-style'
  oStyle.innerHTML = styleContent
  oDiv.className = 'app-loading-wrap'
  oDiv.innerHTML = `<div class="${className}"><div></div></div>`

  return {
    appendLoading() {
      safeDOM.append(document.head, oStyle)
      safeDOM.append(document.body, oDiv)
    },
    removeLoading() {
      safeDOM.remove(document.head, oStyle)
      safeDOM.remove(document.body, oDiv)
    },
  }
}

// ----------------------------------------------------------------------

// Get the loading functions (but we won't use them)
const { appendLoading, removeLoading } = useLoading();

// These are commented out since we're not using the loading functionality
// domReady().then(appendLoading)
// window.onmessage = (ev) => {
//   ev.data.payload === 'removeLoading' && removeLoading()
// }

// setTimeout(removeLoading, 4999)