<?xml version="1.0" encoding="UTF-8"?>
<refactoring-phase id="2" name="Service Foundation">
  <metadata>
    <estimated-duration>3 days</estimated-duration>
    <risk-level>low</risk-level>
    <dependencies>
      <dependency type="phase">phase-1</dependency>
      <dependency type="file">electron/main/api/books-api.ts</dependency>
    </dependencies>
    <next-phase>phase-3</next-phase>
  </metadata>

  <objectives>
    <primary-goal>Extract foundational services from the monolithic books-api.ts file, focusing on isolated, well-defined functionality that can be modernized independently</primary-goal>
    <secondary-goals>
      <goal>Extract OpenLibrary API integration into dedicated service with Zod validation</goal>
      <goal>Extract cover image processing into dedicated service with Sharp modernization</goal>
      <goal>Replace raw HTTP calls with centralized axios client and logging</goal>
      <goal>Establish dependency injection patterns for service architecture</goal>
      <goal>Create foundation for more complex service integrations in Phase 3</goal>
    </secondary-goals>
  </objectives>

  <milestones>
    <milestone id="2.1" name="OpenLibrary Service Foundation">
      <description>OpenLibrary API integration is extracted into dedicated service with modern HTTP client and Zod validation</description>
      <validation>Service handles all external API calls with proper error handling, logging, and data validation</validation>
    </milestone>
    <milestone id="2.2" name="Cover Service Foundation">
      <description>Cover image processing is extracted into dedicated service with Sharp library integration</description>
      <validation>Images are downloaded, processed to WebP format, and standardized to consistent dimensions</validation>
    </milestone>
    <milestone id="2.3" name="Service Architecture Established">
      <description>Dependency injection patterns and service interfaces are established for Phase 3 integration</description>
      <validation>Services can be instantiated independently and follow consistent architectural patterns</validation>
    </milestone>
  </milestones>

  <tasks>
    <task id="2.1" priority="critical" estimated-time="8 hours">
      <name>Extract OpenLibrary Service</name>
      <description>Create dedicated service for all OpenLibrary API interactions, modernizing HTTP handling and adding comprehensive validation</description>
      <files-to-create>
        <file>electron/main/api/books-api/services/openlibrary.service.ts</file>
      </files-to-create>
      <files-to-modify>
        <file>electron/main/api/books-api.ts</file>
      </files-to-modify>
      <codebase-analysis>
        <current-location>
          <function name="searchBooksOnline" lines="568-678" complexity="high">
            <description>Main online search function with caching, ISBN detection, and relevance scoring</description>
            <dependencies>axios, searchBooksByISBN, calculateEnhancedRelevanceScore, preprocessQuery</dependencies>
            <current-issues>Raw axios calls, no validation, console.log logging, complex caching logic</current-issues>
          </function>
          <function name="getBookDetailsFromOpenLibrary" lines="681-857" complexity="high">
            <description>Fetches detailed book information from OpenLibrary work and edition APIs</description>
            <dependencies>axios, multiple API endpoints, author fetching, edition processing</dependencies>
            <current-issues>Raw axios calls, no validation, manual error handling, nested API calls</current-issues>
          </function>
          <function name="searchBooksByISBN" lines="532-565" complexity="medium">
            <description>Specialized ISBN search using OpenLibrary books API</description>
            <dependencies>axios, ISBN cleaning logic</dependencies>
            <current-issues>Raw axios calls, hardcoded timeout values, no validation</current-issues>
          </function>
        </current-location>
      </codebase-analysis>
      <implementation-details>
        <step>Analyze existing OpenLibrary functions in books-api.ts (lines 532-857)</step>
        <step>Create OpenLibraryService class with dependency injection constructor</step>
        <step>Extract searchBooksOnline function (lines 568-678) with caching logic</step>
        <step>Extract getBookDetailsFromOpenLibrary function (lines 681-857) with nested API calls</step>
        <step>Extract searchBooksByISBN helper function (lines 532-565)</step>
        <step>Replace all raw axios calls with injected httpClient from Phase 1</step>
        <step>Add comprehensive Zod validation for all OpenLibrary API responses</step>
        <step>Replace console.log with structured logging using injected logger</step>
        <step>Implement proper error handling with meaningful error messages</step>
        <step>Preserve existing caching mechanism but make it configurable</step>
      </implementation-details>
      <code-structure>
        <class name="OpenLibraryService">
          <constructor>
            <parameter name="httpClient" type="AxiosInstance" description="Centralized HTTP client from Phase 1 with logging interceptors" />
            <parameter name="logger" type="Logger" description="Scoped logger from Phase 1 for structured logging" />
          </constructor>
          <method name="searchBooks" return-type="Promise<BookSearchResult[]>">
            <parameter name="query" type="string" description="Search term (title, author, ISBN)" />
            <parameter name="limit" type="number" default="10" description="Maximum results to return" />
            <parameter name="localBooks" type="Book[]" default="[]" description="Local books for deduplication scoring" />
          </method>
          <method name="searchBooksByISBN" return-type="Promise<BookSearchResult[]>">
            <parameter name="isbn" type="string" description="ISBN to search for (will be cleaned automatically)" />
          </method>
          <method name="getBookDetails" return-type="Promise<Partial<Book>>">
            <parameter name="olid" type="string" description="OpenLibrary work ID (e.g., 'OL123456W')" />
          </method>
          <method name="getCoverUrl" return-type="string | null">
            <parameter name="coverId" type="number" description="OpenLibrary cover ID" />
            <parameter name="size" type="'S' | 'M' | 'L'" default="'L'" description="Cover size (Small, Medium, Large)" />
          </method>
          <private-method name="validateSearchResponse" return-type="BookSearchResult[]">
            <parameter name="data" type="any" description="Raw API response to validate" />
          </private-method>
          <private-method name="validateBookDetails" return-type="Partial<Book>">
            <parameter name="data" type="any" description="Raw book details to validate" />
          </private-method>
        </class>
      </code-structure>
      <modernization-actions>
        <action type="http-client">Replace all axios.get() calls with this.httpClient.get() for consistent logging and error handling</action>
        <action type="validation">Add OpenLibraryBookSchema.array().safeParse() validation for search responses</action>
        <action type="validation">Add OpenLibraryWorkSchema.safeParse() validation for book details responses</action>
        <action type="logging">Replace all console.log/console.error with this.logger.info/error for structured logging</action>
        <action type="error-handling">Implement try-catch blocks with meaningful error messages and proper error propagation</action>
        <action type="caching">Preserve existing search cache but make it a class property for better encapsulation</action>
        <action type="timeout">Use httpClient timeout configuration instead of hardcoded values</action>
        <action type="user-agent">Remove hardcoded User-Agent headers (handled by httpClient)</action>
      </modernization-actions>
      <implementation-guidance>
        <guidance type="function-extraction">
          <source-location>books-api.ts lines 568-678 (searchBooksOnline)</source-location>
          <target-method>searchBooks</target-method>
          <key-considerations>
            <consideration>Preserve caching logic but move cache to class properties</consideration>
            <consideration>Maintain query preprocessing and ISBN detection logic</consideration>
            <consideration>Keep relevance scoring integration (will be moved to SearchService in Phase 3)</consideration>
            <consideration>Handle both regular search and ISBN search paths</consideration>
          </key-considerations>
        </guidance>
        <guidance type="function-extraction">
          <source-location>books-api.ts lines 681-857 (getBookDetailsFromOpenLibrary)</source-location>
          <target-method>getBookDetails</target-method>
          <key-considerations>
            <consideration>Handle nested API calls for work details, author details, and editions</consideration>
            <consideration>Preserve retry logic for author fetching</consideration>
            <consideration>Maintain language code conversion and publication date extraction</consideration>
            <consideration>Keep cover URL generation logic</consideration>
          </key-considerations>
        </guidance>
        <guidance type="helper-functions">
          <source-location>books-api.ts lines 532-565 (searchBooksByISBN)</source-location>
          <target-method>searchBooksByISBN</target-method>
          <key-considerations>
            <consideration>Preserve ISBN cleaning logic (remove hyphens and spaces)</consideration>
            <consideration>Maintain high relevance score for exact ISBN matches</consideration>
            <consideration>Handle empty responses gracefully</consideration>
          </key-considerations>
        </guidance>
      </implementation-guidance>
      <success-criteria>
        <criterion>Service compiles without TypeScript errors</criterion>
        <criterion>All API responses are validated with Zod schemas</criterion>
        <criterion>Network requests use centralized HTTP client with logging</criterion>
        <criterion>Proper error handling with meaningful messages is implemented</criterion>
        <criterion>Existing functionality is preserved (search results, book details, caching)</criterion>
        <criterion>Service can be instantiated independently with dependency injection</criterion>
      </success-criteria>
      <risks>
        <risk severity="medium">OpenLibrary API response format changes breaking validation</risk>
        <risk severity="low">Network timeout configuration issues with httpClient</risk>
        <risk severity="low">Cache behavior changes affecting performance</risk>
      </risks>
    </task>

    <task id="2.2" priority="critical" estimated-time="6 hours">
      <name>Extract Cover Service</name>
      <description>Create dedicated service for cover image processing, modernizing with Sharp library for optimal performance and standardized output</description>
      <files-to-create>
        <file>electron/main/api/books-api/services/cover.service.ts</file>
      </files-to-create>
      <files-to-modify>
        <file>electron/main/api/books-api.ts</file>
      </files-to-modify>
      <codebase-analysis>
        <current-location>
          <function name="downloadCoverImageData" lines="122-188" complexity="high">
            <description>Downloads cover images with redirect handling, timeout management, and error recovery</description>
            <dependencies>node:http, node:https, URL parsing, recursive redirect handling</dependencies>
            <current-issues>Raw HTTP/HTTPS modules, no image processing, no format standardization, complex redirect logic</current-issues>
          </function>
          <function name="getOpenLibraryCoverUrl" lines="117-119" complexity="low">
            <description>Helper function to generate OpenLibrary cover URLs</description>
            <dependencies>None</dependencies>
            <current-issues>Hardcoded URL format, no validation</current-issues>
          </function>
          <usage-locations>
            <location>checkAndDownloadMissingCovers (lines 1007-1028) - batch cover downloading</location>
            <location>createBookWithValidation (lines 1049-1060) - cover download during book creation</location>
            <location>addBookFromOpenLibrary (lines 1018-1019) - cover processing for OpenLibrary imports</location>
          </usage-locations>
        </current-location>
      </codebase-analysis>
      <implementation-details>
        <step>Analyze existing cover logic in books-api.ts (lines 122-188 and usage locations)</step>
        <step>Create CoverService class with dependency injection constructor</step>
        <step>Extract downloadCoverImageData function (lines 122-188) with redirect handling</step>
        <step>Extract getOpenLibraryCoverUrl helper function (lines 117-119)</step>
        <step>Replace raw HTTP/HTTPS modules with injected httpClient for consistency</step>
        <step>Implement Sharp-based image processing pipeline</step>
        <step>Add comprehensive image validation (format, size, corruption checks)</step>
        <step>Standardize all output to WebP format with quality optimization</step>
        <step>Add image dimension standardization (800x1200 max with aspect ratio preservation)</step>
        <step>Implement proper error handling with detailed error messages</step>
        <step>Add structured logging for all image processing operations</step>
      </implementation-details>
      <code-structure>
        <class name="CoverService">
          <constructor>
            <parameter name="httpClient" type="AxiosInstance" description="Centralized HTTP client for consistent downloading" />
            <parameter name="logger" type="Logger" description="Scoped logger for image processing operations" />
          </constructor>
          <method name="processCoverUrl" return-type="Promise<Buffer>">
            <parameter name="url" type="string" description="Cover image URL to download and process" />
            <description>Main entry point: downloads image and processes it to standardized WebP format</description>
          </method>
          <method name="downloadImageBuffer" return-type="Promise<Buffer>">
            <parameter name="url" type="string" description="Image URL to download" />
            <description>Downloads image using httpClient, replacing raw HTTP/HTTPS logic</description>
          </method>
          <method name="validateImageBuffer" return-type="boolean">
            <parameter name="buffer" type="Buffer" description="Image buffer to validate" />
            <description>Validates image format, size, and integrity</description>
          </method>
          <method name="standardizeImage" return-type="Promise<Buffer>">
            <parameter name="buffer" type="Buffer" description="Raw image buffer to process" />
            <description>Processes image with Sharp: resize, convert to WebP, optimize quality</description>
          </method>
          <method name="getOpenLibraryCoverUrl" return-type="string">
            <parameter name="coverId" type="number" description="OpenLibrary cover ID" />
            <parameter name="size" type="'S' | 'M' | 'L'" default="'L'" description="Cover size" />
            <description>Generates OpenLibrary cover URLs with validation</description>
          </method>
          <private-method name="processWithSharp" return-type="Promise<Buffer>">
            <parameter name="buffer" type="Buffer" description="Image buffer to process with Sharp" />
            <description>Internal Sharp processing pipeline</description>
          </private-method>
        </class>
      </code-structure>
      <modernization-actions>
        <action type="http-replacement">Replace raw node:http/https modules with this.httpClient.get() for consistent behavior</action>
        <action type="sharp-integration">Add Sharp processing pipeline: resize(800, 1200, { fit: 'inside', withoutEnlargement: true })</action>
        <action type="format-conversion">Convert all images to WebP format with quality: 80 for optimal size/quality balance</action>
        <action type="validation">Add comprehensive image validation: format detection, size limits, corruption checks</action>
        <action type="error-handling">Implement detailed error handling for download failures, processing errors, and invalid formats</action>
        <action type="logging">Replace console.log with structured logging for all image operations</action>
        <action type="memory-optimization">Use Sharp streaming where possible to minimize memory usage</action>
        <action type="redirect-handling">Simplify redirect handling by leveraging httpClient's built-in redirect support</action>
      </modernization-actions>
      <implementation-guidance>
        <guidance type="function-extraction">
          <source-location>books-api.ts lines 122-188 (downloadCoverImageData)</source-location>
          <target-method>processCoverUrl</target-method>
          <key-considerations>
            <consideration>Current function handles redirects manually - replace with httpClient automatic redirect handling</consideration>
            <consideration>Preserve timeout behavior but use httpClient configuration</consideration>
            <consideration>Maintain error handling for network failures and invalid responses</consideration>
            <consideration>Add Sharp processing pipeline after successful download</consideration>
          </key-considerations>
        </guidance>
        <guidance type="sharp-integration">
          <processing-pipeline>
            <step>Download image buffer using httpClient</step>
            <step>Validate buffer is valid image format</step>
            <step>Process with Sharp: sharp(buffer).resize(800, 1200, { fit: 'inside', withoutEnlargement: true })</step>
            <step>Convert to WebP: .webp({ quality: 80, effort: 4 })</step>
            <step>Return processed buffer</step>
          </processing-pipeline>
        </guidance>
        <guidance type="usage-locations">
          <location>checkAndDownloadMissingCovers - batch processing will use new service</location>
          <location>createBookWithValidation - cover download will use new service</location>
          <location>addBookFromOpenLibrary - OpenLibrary cover processing will use new service</location>
        </guidance>
      </implementation-guidance>
      <success-criteria>
        <criterion>All images are processed to consistent WebP format with 80% quality</criterion>
        <criterion>Image dimensions are standardized to max 800x1200 with aspect ratio preservation</criterion>
        <criterion>Invalid or corrupted images are properly rejected with meaningful errors</criterion>
        <criterion>Memory usage is optimized through Sharp's efficient processing</criterion>
        <criterion>Network requests use centralized httpClient with proper logging</criterion>
        <criterion>Service can be instantiated independently with dependency injection</criterion>
      </success-criteria>
      <risks>
        <risk severity="medium">Sharp library compatibility issues on different platforms</risk>
        <risk severity="low">Memory usage spikes with very large images</risk>
        <risk severity="low">WebP format compatibility with older systems</risk>
      </risks>
    </task>

  </tasks>

  <validation-steps>
    <step id="1" name="Service Compilation">
      <description>Verify both services compile without TypeScript errors</description>
      <command>npx tsc --noEmit</command>
      <expected-result>No TypeScript compilation errors for OpenLibraryService and CoverService</expected-result>
    </step>
    <step id="2" name="OpenLibrary Service Integration">
      <description>Test OpenLibrary service with real API calls and validation</description>
      <command>Create test script: const service = new OpenLibraryService(httpClient, logger); await service.searchBooks('test');</command>
      <expected-result>API calls return validated BookSearchResult[] with proper Zod validation</expected-result>
    </step>
    <step id="3" name="Cover Service Processing">
      <description>Test cover service with sample images and Sharp processing</description>
      <command>Create test script: const service = new CoverService(httpClient, logger); await service.processCoverUrl('test-image-url');</command>
      <expected-result>Images downloaded, processed to WebP format with standardized dimensions</expected-result>
    </step>
    <step id="4" name="Service Independence">
      <description>Verify services can be instantiated independently</description>
      <command>Test dependency injection: new OpenLibraryService(httpClient, logger), new CoverService(httpClient, logger)</command>
      <expected-result>Services instantiate without errors and accept injected dependencies</expected-result>
    </step>
    <step id="5" name="Logging Integration">
      <description>Verify structured logging works correctly</description>
      <command>Run services and check log output for proper formatting and scoping</command>
      <expected-result>All log messages use structured format with books-api scope</expected-result>
    </step>
  </validation-steps>

  <risk-assessment>
    <overall-risk>Low</overall-risk>
    <risk-factors>
      <factor name="OpenLibrary API Changes" probability="medium" impact="medium">
        <mitigation>Comprehensive Zod validation with safeParse() and detailed error handling</mitigation>
      </factor>
      <factor name="Sharp Library Compatibility" probability="low" impact="medium">
        <mitigation>Sharp is well-established with broad platform support; include fallback error handling</mitigation>
      </factor>
      <factor name="HTTP Client Integration" probability="low" impact="low">
        <mitigation>httpClient from Phase 1 is already tested; maintain existing timeout and error handling patterns</mitigation>
      </factor>
      <factor name="Service Isolation" probability="low" impact="low">
        <mitigation>Services are designed to be independent with clear interfaces and dependency injection</mitigation>
      </factor>
    </risk-factors>
  </risk-assessment>

  <success-criteria>
    <criterion>OpenLibraryService is fully functional with Zod validation and centralized HTTP client</criterion>
    <criterion>CoverService processes images to standardized WebP format with Sharp</criterion>
    <criterion>Both services use dependency injection and can be instantiated independently</criterion>
    <criterion>All network requests use centralized httpClient with proper logging</criterion>
    <criterion>Structured logging is implemented throughout both services</criterion>
    <criterion>Services maintain existing functionality while modernizing implementation</criterion>
    <criterion>Foundation is established for Phase 3 service integration</criterion>
  </success-criteria>

  <cross-references>
    <reference phase="phase-1" task="1.3">
      <description>Uses centralized logger created in Phase 1</description>
    </reference>
    <reference phase="phase-1" task="1.4">
      <description>Uses centralized HTTP client created in Phase 1</description>
    </reference>
    <reference phase="phase-1" task="1.5">
      <description>Uses Zod schemas created in Phase 1 for validation</description>
    </reference>
    <reference phase="phase-3" task="3.1">
      <description>OpenLibraryService will be integrated into SearchService in Phase 3</description>
    </reference>
    <reference phase="phase-3" task="3.2">
      <description>CoverService will be integrated into BookService in Phase 3</description>
    </reference>
    <reference phase="phase-4" task="4.2">
      <description>Both services will be unit tested in Phase 4</description>
    </reference>
  </cross-references>
</refactoring-phase>
