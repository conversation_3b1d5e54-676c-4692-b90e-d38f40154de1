import log from 'electron-log';

// Configure the logger with environment-aware levels
log.transports.file.level = process.env.NODE_ENV === 'production' ? 'info' : 'debug';
log.transports.console.level = process.env.NODE_ENV === 'production' ? 'warn' : 'debug';
log.transports.file.format = '{y}-{m}-{d} {h}:{i}:{s}.{ms} [{level}] {text}';
log.transports.console.format = '{h}:{i}:{s}.{ms} [{level}] {text}';

// Add a category for easy filtering
const logger = log.scope('books-api');

export default logger;
