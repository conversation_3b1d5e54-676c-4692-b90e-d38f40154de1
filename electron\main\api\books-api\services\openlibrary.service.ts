import { AxiosInstance } from 'axios';
import httpClient from '../utils/http.client';
import logger from '../utils/logger';
import {
  OpenLibrarySearchResultSchema,
  OpenLibraryWorkDetailsSchema,
  OpenLibraryEditionDetailsSchema,
  OpenLibraryAuthorDetailsSchema,
  OpenLibraryISBNResponseSchema,
  BookSearchResultSchema
} from '../book.schemas';
import {
  OpenLibrarySearchResult,
  OpenLibraryWorkDetails,
  OpenLibraryEditionDetails,
  OpenLibraryAuthorDetails,
  OpenLibraryISBNResponse,
  BookSearchResult,
  Book
} from '../book.types';
import { convertLanguageCode } from '../../../utils/language-converter';
import { createHash } from 'node:crypto';

/**
 * Service for handling all OpenLibrary API interactions
 * Provides modern HTTP client usage, comprehensive validation, and structured logging
 */
export class OpenLibraryService {
  private readonly httpClient: AxiosInstance;
  private readonly logger: typeof logger;
  private readonly searchCache = new Map<string, BookSearchResult[]>();
  private readonly cacheTimestamps = new Map<string, number>();
  private readonly CACHE_EXPIRY_TIME = 5 * 60 * 1000; // 5 minutes

  constructor(
    httpClientInstance: AxiosInstance = httpClient,
    loggerInstance: typeof logger = logger
  ) {
    this.httpClient = httpClientInstance;
    this.logger = loggerInstance;
  }

  /**
   * Search books using OpenLibrary API with enhanced algorithm
   * @param query Search term (title, author, ISBN)
   * @param limit Maximum results to return (max 10)
   * @param localBooks Local books for deduplication scoring
   * @returns Promise of validated book search results
   */
  async searchBooks(
    query: string,
    limit: number = 10,
    localBooks: Book[] = []
  ): Promise<BookSearchResult[]> {
    try {
      // Ensure limit doesn't exceed 10
      const searchLimit = Math.min(limit, 10);

      // Early return for very short queries
      if (query.trim().length < 2) {
        this.logger.info('Query too short, skipping search', { query });
        return [];
      }

      // Check cache first — include a comprehensive content hash of local books to avoid stale caches
      const localFingerprint = (() => {
        try {
          // Build deterministic tuples with fields that affect dedupe/ranking
          const tuples = localBooks.map(b => `${b.id ?? ''}|${b.isbn ?? ''}|${(b as any).olid ?? ''}|${b.updated_at ?? b.created_at ?? ''}`);
          // Sort for stability regardless of input order
          tuples.sort();
          // Use crypto hash for robustness
          const hasher = createHash('sha1');
          for (const t of tuples) hasher.update(t);
          return hasher.digest('hex').slice(0, 16); // short digest for key size
        } catch {
          return '0';
        }
      })();
      const cacheKey = `${query}_${searchLimit}_${localFingerprint}`;
      const cachedTime = this.cacheTimestamps.get(cacheKey);
      const now = Date.now();

      if (cachedTime && (now - cachedTime) < this.CACHE_EXPIRY_TIME) {
        const cachedResults = this.searchCache.get(cacheKey);
        if (cachedResults) {
          this.logger.info('Returning cached results', { query, resultCount: cachedResults.length });
          return cachedResults;
        }
      }

      const isISBNQuery = this.isISBN(query);
      let books: BookSearchResult[] = [];

      if (isISBNQuery) {
        // Use ISBN-specific search
        books = await this.searchBooksByISBN(query);
        // Apply enhanced scoring even for ISBN results
        books = books.map(book => ({
          ...book,
          relevanceScore: this.calculateEnhancedRelevanceScore(book, [query], isISBNQuery, localBooks)
        }));
      } else {
        // Preprocess query for better fuzzy matching
        const queryVariations = this.preprocessQuery(query);
        const primaryQuery = queryVariations[0];

        // Use regular search with reduced initial limit for better performance
        const encodedQuery = encodeURIComponent(primaryQuery);
        const url = `https://openlibrary.org/search.json?q=${encodedQuery}&limit=25&fields=title,author_name,isbn,cover_i,cover_edition_key,first_publish_year,key,subject,publisher,edition_count,language`;

        const response = await this.httpClient.get(url);

        // Validate response structure and get validated docs
        const validationResult = this.validateSearchResponse(response.data);
        if (!validationResult.success || !validationResult.validatedDocs) {
          this.logger.error('Invalid search response from OpenLibrary', {
            query,
            error: validationResult.error
          });
          return [];
        }

        // Process and score results with enhanced algorithm using validated docs
        const scoredBooks = validationResult.validatedDocs.map((book: OpenLibrarySearchResult) => {
          const olid = book.key ? book.key.split('/').pop() : undefined;

          const result: BookSearchResult = {
            ...book,
            olid,
            cover_url: book.cover_i ? this.getCoverUrl(book.cover_i, 'M') : undefined,
            genres: book.subject ? this.processGenre(book.subject) : undefined,
            isbn_primary: book.isbn ? book.isbn[0] : undefined,
            relevanceScore: this.calculateEnhancedRelevanceScore(book, queryVariations, isISBNQuery, localBooks)
          };

          return result;
        });

        // Sort by relevance score and take top results
        books = scoredBooks
          .filter(book => book.relevanceScore && book.relevanceScore > 0)
          .sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
          .slice(0, searchLimit);
      }

      // Cache the results
      this.searchCache.set(cacheKey, books);
      this.cacheTimestamps.set(cacheKey, now);

      // Clean up old cache entries periodically
      this.cleanupCache();

      this.logger.info('Search completed', { 
        query, 
        resultCount: books.length, 
        withCovers: books.filter(b => b.cover_i || b.cover_edition_key).length 
      });
      
      return books;

    } catch (error) {
      this.logger.error('Error searching books online', { query, error });
      // Return empty array for graceful degradation
      return [];
    }
  }

  /**
   * Search books by ISBN using OpenLibrary books API
   * @param isbn ISBN to search for (will be cleaned automatically)
   * @returns Promise of validated book search results
   */
  async searchBooksByISBN(isbn: string): Promise<BookSearchResult[]> {
    try {
      const cleanISBN = isbn.replace(/[-\s]/g, '');
      const url = `https://openlibrary.org/api/books?bibkeys=ISBN:${cleanISBN}&format=json&jscmd=data`;

      const response = await this.httpClient.get(url);

      // Validate the ISBN API response with Zod
      const validationResult = OpenLibraryISBNResponseSchema.safeParse(response.data);
      if (!validationResult.success) {
        this.logger.error('Invalid ISBN response from OpenLibrary', {
          isbn: cleanISBN,
          error: validationResult.error
        });
        return [];
      }

      const data = validationResult.data;
      const isbnKey = `ISBN:${cleanISBN}`;
      const books: BookSearchResult[] = [];

      if (data[isbnKey]) {
        const book = data[isbnKey];

        // Extract cover ID from cover URL if available
        let coverId: number | undefined;
        if (book.cover?.medium) {
          const coverMatch = book.cover.medium.match(/\/(\d+)-M\.jpg$/);
          if (coverMatch) {
            coverId = parseInt(coverMatch[1]);
          }
        }

        // Extract publication year from publish_date
        let publishYear: number | undefined;
        if (book.publish_date) {
          const yearMatch = book.publish_date.match(/\b(\d{4})\b/);
          if (yearMatch) {
            publishYear = parseInt(yearMatch[1]);
          }
        }

        const result: BookSearchResult = {
          title: book.title,
          author_name: book.authors ? book.authors.map(a => a.name) : [],
          cover_i: coverId,
          first_publish_year: publishYear,
          isbn: book.identifiers?.isbn_13 || book.identifiers?.isbn_10 || [cleanISBN],
          publisher: book.publishers ? book.publishers.map(p => p.name) : [],
          language: book.languages ? book.languages.map(l => convertLanguageCode(l.key.replace('/languages/', ''))) : [],
          cover_url: book.cover?.medium,
          isbn_primary: cleanISBN,
          relevanceScore: 10000 // Highest score for exact ISBN match
        };

        books.push(result);

        this.logger.info('ISBN search completed successfully', {
          isbn: cleanISBN,
          title: result.title,
          hasCover: !!result.cover_i
        });
      } else {
        this.logger.info('No results found for ISBN', { isbn: cleanISBN });
      }

      return books;
    } catch (error) {
      this.logger.error('Error searching books by ISBN', { isbn, error });
      return [];
    }
  }

  /**
   * Get detailed book information from OpenLibrary work ID
   * @param olid OpenLibrary work ID (e.g., 'OL123456W')
   * @returns Promise of partial book data
   */
  async getBookDetails(olid: string): Promise<Partial<Book>> {
    try {
      // First, get work details
      const workUrl = `https://openlibrary.org/works/${olid}.json`;
      const workResponse = await this.httpClient.get<OpenLibraryWorkDetails>(workUrl);

      // Validate work response
      const workValidation = OpenLibraryWorkDetailsSchema.safeParse(workResponse.data);
      if (!workValidation.success) {
        this.logger.error('Invalid work details response', { olid, error: workValidation.error });
        throw new Error('Invalid work details from OpenLibrary');
      }

      const work = workValidation.data;
      let authorNames: string[] = [];
      let description = '';
      let genres = '';

      // Get author names with retry logic
      if (work.authors && work.authors.length > 0) {
        authorNames = await this.fetchAuthorNames(work.authors);
      }

      // Process description
      description = this.processDescription(work.description);

      // Process genres/subjects
      if (work.subjects && work.subjects.length > 0) {
        genres = this.processGenre(work.subjects) || '';
      }

      // Try to get edition details for more specific information
      const editionDetails = await this.fetchEditionDetails(olid, work);

      return {
        title: work.title,
        author: authorNames.length > 0 ? authorNames.join(', ') : null,
        isbn: editionDetails.isbn || null,
        description: description || null,
        genres: genres || null,
        page_count: editionDetails.pageCount,
        publication_date: this.extractPublicationYear(editionDetails.publicationDate),
        language: editionDetails.language || null,
        olid,
        cover_url: editionDetails.coverUrl || null
      };

    } catch (error) {
      this.logger.error('Error getting book details from OpenLibrary', { olid, error });
      throw new Error('Failed to get book details from OpenLibrary');
    }
  }

  /**
   * Generate OpenLibrary cover URL
   * @param coverId OpenLibrary cover ID
   * @param size Cover size (Small, Medium, Large)
   * @returns Cover URL string
   */
  getCoverUrl(coverId: number, size: 'S' | 'M' | 'L' = 'L'): string {
    return `https://covers.openlibrary.org/b/id/${coverId}-${size}.jpg`;
  }

  /**
   * Validate OpenLibrary search response and return validated docs
   * @param data Raw API response to validate
   * @returns Validation result with validated docs or error
   */
  private validateSearchResponse(data: any): {
    success: boolean;
    validatedDocs?: OpenLibrarySearchResult[];
    error?: any
  } {
    try {
      // Validate the overall structure
      if (!data || !data.docs || !Array.isArray(data.docs)) {
        return { success: false, error: 'Invalid response structure' };
      }

      // Validate the entire docs array with Zod
      const docsValidation = OpenLibrarySearchResultSchema.array().safeParse(data.docs);

      if (!docsValidation.success) {
        this.logger.error('Search response docs validation failed', {
          error: docsValidation.error,
          docsCount: data.docs.length
        });

        // Try to validate individual docs and filter out invalid ones
        const validatedBooks = data.docs.map((book: any) => {
          const result = OpenLibrarySearchResultSchema.safeParse(book);
          if (!result.success) {
            this.logger.warn('Invalid book in search response, skipping', {
              book: book.title || 'Unknown',
              error: result.error.issues?.[0]?.message
            });
            return null;
          }
          return result.data;
        }).filter(Boolean) as OpenLibrarySearchResult[];

        if (validatedBooks.length === 0) {
          return { success: false, error: 'No valid books found in response' };
        }

        this.logger.warn('Using partially validated search results', {
          originalCount: data.docs.length,
          validCount: validatedBooks.length
        });

        return { success: true, validatedDocs: validatedBooks };
      }

      return { success: true, validatedDocs: docsValidation.data };
    } catch (error) {
      return { success: false, error };
    }
  }

  /**
   * Determines if a string is likely an ISBN (10 or 13 digits)
   * @param query The search query
   * @returns True if the query appears to be an ISBN
   */
  private isISBN(query: string): boolean {
    // Remove any hyphens or spaces that might be in an ISBN
    const cleanQuery = query.replace(/[-\s]/g, '');

    // ISBN-10: 10 digits (can end with X)
    const isISBN10 = /^(\d{9}[\dX])$/.test(cleanQuery);
    // ISBN-13: 13 digits starting with 978 or 979
    const isISBN13 = /^(978|979)\d{10}$/.test(cleanQuery);

    return isISBN10 || isISBN13;
  }

  /**
   * Preprocess query for better fuzzy matching
   * @param query Original search query
   * @returns Array of query variations
   */
  private preprocessQuery(query: string): string[] {
    const original = query.trim();
    const variations = [original];

    // Add variation without common words
    const withoutCommonWords = original
      .replace(/\b(the|a|an|and|or|but|in|on|at|to|for|of|with|by)\b/gi, ' ')
      .replace(/\s+/g, ' ')
      .trim();
    if (withoutCommonWords !== original && withoutCommonWords.length > 0) {
      variations.push(withoutCommonWords);
    }

    // Add simple typo-correction map similar to legacy behavior
    const typoMap: Record<string, string> = {
      lrod: 'lord',
      teh: 'the',
      adn: 'and',
      fo: 'of',
      taht: 'that',
      hte: 'the'
    };
    const corrected = original
      .toLowerCase()
      .split(/\s+/)
      .map((w) => typoMap[w] || w)
      .join(' ')
      .trim();
    if (corrected !== original.toLowerCase() && corrected.length > 0) {
      variations.push(corrected);
    }

    return variations;
  }

  /**
   * Calculate enhanced relevance score for search results
   * @param book Book search result
   * @param queryVariations Array of query variations
   * @param isISBNQuery Whether this is an ISBN search
   * @param localBooks Local books for deduplication
   * @returns Relevance score
   */
  private calculateEnhancedRelevanceScore(
    book: OpenLibrarySearchResult,
    queryVariations: string[],
    isISBNQuery: boolean,
    localBooks: Book[]
  ): number {
    if (isISBNQuery) {
      return 10000; // Highest score for ISBN matches
    }

    let score = 0;
    const primaryQuery = queryVariations[0].toLowerCase();

    // Title matching (highest weight)
    if (book.title) {
      const titleLower = book.title.toLowerCase();
      if (titleLower.includes(primaryQuery)) {
        score += 1000;
      }
      // Exact title match gets bonus
      if (titleLower === primaryQuery) {
        score += 2000;
      }
    }

    // Author matching
    if (book.author_name && book.author_name.length > 0) {
      const authorText = book.author_name.join(' ').toLowerCase();
      if (authorText.includes(primaryQuery)) {
        score += 500;
      }
    }

    // Publication year bonus for recent books
    if (book.first_publish_year) {
      const currentYear = new Date().getFullYear();
      const yearDiff = currentYear - book.first_publish_year;
      if (yearDiff < 10) score += 100;
      else if (yearDiff < 20) score += 50;
    }

    // Cover availability bonus
    if (book.cover_i || book.cover_edition_key) {
      score += 200;
    }

    // ISBN availability bonus
    if (book.isbn && book.isbn.length > 0) {
      score += 100;
    }

    // Deduplication penalty for books already in local library
    if (localBooks.length > 0) {
      const isDuplicate = localBooks.some(localBook => {
        return (
          (book.isbn && localBook.isbn && book.isbn.includes(localBook.isbn)) ||
          (book.title && localBook.title &&
           book.title.toLowerCase() === localBook.title.toLowerCase())
        );
      });
      if (isDuplicate) {
        score = Math.max(1, score * 0.1); // Reduce score significantly but keep > 0
      }
    }

    return Math.max(0, score);
  }

  /**
   * Fetch author names with retry logic
   * @param authors Array of author references from work details
   * @returns Array of author names
   */
  private async fetchAuthorNames(authors: OpenLibraryWorkDetails['authors']): Promise<string[]> {
    if (!authors || authors.length === 0) return [];

    const authorPromises = authors.map(async (author) => {
      const authorUrl = `https://openlibrary.org${author.author.key}.json`;

      // Try with retry logic
      for (let attempt = 1; attempt <= 2; attempt++) {
        try {
          const authorResponse = await this.httpClient.get<OpenLibraryAuthorDetails>(authorUrl);

          // Validate author response
          const validation = OpenLibraryAuthorDetailsSchema.safeParse(authorResponse.data);
          if (!validation.success) {
            this.logger.warn('Invalid author details response', {
              authorUrl,
              error: validation.error
            });
            return null;
          }

          return validation.data.name;
        } catch (error) {
          if (attempt === 1) {
            const isTimeout = this.isTimeoutError(error);
            const errorType = isTimeout ? 'timeout' : 'network error';
            this.logger.warn(`Failed to fetch author details (${errorType}), retrying`, {
              authorUrl,
              attempt,
              errorType,
              error
            });
            // Wait 1 second before retry
            await new Promise(resolve => setTimeout(resolve, 1000));
          } else {
            const isTimeout = this.isTimeoutError(error);
            const errorType = isTimeout ? 'timeout' : 'network error';
            this.logger.warn(`Failed to fetch author details (${errorType}) after retries`, {
              authorUrl,
              attempts: attempt,
              errorType,
              error
            });
            return null;
          }
        }
      }
      return null;
    });

    try {
      const authors_results = await Promise.all(authorPromises);
      const validNames = authors_results.filter(name => name !== null) as string[];

      // If no author names were found, try to extract basic names from author keys as fallback
      if (validNames.length === 0 && authors && authors.length > 0) {
        this.logger.info('Using author keys as fallback for author names');
        return authors.map(author => {
          // Extract author name from key like /authors/OL2745568A -> OL2745568A
          const authorKey = author.author.key.split('/').pop() || 'Unknown Author';
          return authorKey.replace(/^OL|A$/g, '') || 'Unknown Author'; // Remove OL prefix and A suffix if present
        });
      }

      return validNames;
    } catch (error) {
      this.logger.warn('Failed to fetch some author details, continuing with available data', { error });
      return [];
    }
  }

  /**
   * Process description from work details
   * @param description Raw description from OpenLibrary
   * @returns Processed description string
   */
  private processDescription(description: OpenLibraryWorkDetails['description']): string {
    if (!description) return '';

    if (typeof description === 'string') {
      return description;
    } else if (description.value) {
      return description.value;
    }

    return '';
  }

  /**
   * Process genre from subjects array
   * @param subjects Array of subjects/genres
   * @returns Processed genre string
   */
  private processGenre(subjects: string[]): string | null {
    if (!subjects || subjects.length === 0) return null;

    const firstGenre = subjects[0].trim();
    if (!firstGenre) return null;

    const firstWord = firstGenre.split(/\s+/)[0];
    return firstWord.charAt(0).toUpperCase() + firstWord.slice(1).toLowerCase();
  }

  /**
   * Fetch edition details for more specific book information
   * @param olid OpenLibrary work ID
   * @param work Work details
   * @returns Edition details object
   */
  private async fetchEditionDetails(olid: string, work: OpenLibraryWorkDetails): Promise<{
    isbn: string | null;
    pageCount: number | null;
    publicationDate: string;
    language: string | null;
    coverUrl: string | null;
  }> {
    let isbn = '';
    let pageCount: number | null = null;
    let publicationDate = '';
    let language = '';
    let coverUrl = '';

    try {
      // Get editions for this work
      const editionsUrl = `https://openlibrary.org/works/${olid}/editions.json`;
      const editionsResponse = await this.httpClient.get(editionsUrl);

      const editions = editionsResponse.data.entries;
      if (editions && editions.length > 0) {
        // Find the best edition (preferably with ISBN and page count)
        const bestEdition = editions.find((ed: OpenLibraryEditionDetails) =>
          (ed.isbn_13 && ed.isbn_13.length > 0) || (ed.isbn_10 && ed.isbn_10.length > 0)
        ) || editions[0];

        if (bestEdition) {
          // Validate edition details
          const validation = OpenLibraryEditionDetailsSchema.safeParse(bestEdition);
          if (validation.success) {
            const edition = validation.data;

            // Get ISBN (prefer ISBN-13)
            if (edition.isbn_13 && edition.isbn_13.length > 0) {
              isbn = edition.isbn_13[0];
            } else if (edition.isbn_10 && edition.isbn_10.length > 0) {
              isbn = edition.isbn_10[0];
            }

            // Get page count
            if (edition.number_of_pages) {
              pageCount = edition.number_of_pages;
            }

            // Get publication date
            if (edition.publish_date) {
              publicationDate = this.extractPublicationYear(edition.publish_date);
            } else if (work.first_publish_date) {
              publicationDate = this.extractPublicationYear(work.first_publish_date);
            }

            // Get language
            if (edition.languages && edition.languages.length > 0) {
              const langKey = edition.languages[0].key;
              const rawLanguageCode = langKey.replace('/languages/', '');
              language = convertLanguageCode(rawLanguageCode);
            }

            // Get cover
            if (edition.covers && edition.covers.length > 0) {
              coverUrl = this.getCoverUrl(edition.covers[0]);
            } else if (work.covers && work.covers.length > 0) {
              coverUrl = this.getCoverUrl(work.covers[0]);
            }
          }
        }
      }
    } catch (error) {
      this.logger.warn('Failed to fetch edition details', { olid, error });
      // Use work-level data as fallback
      if (work.first_publish_date) {
        publicationDate = this.extractPublicationYear(work.first_publish_date);
      }
      if (work.covers && work.covers.length > 0) {
        coverUrl = this.getCoverUrl(work.covers[0]);
      }
    }

    return {
      isbn: isbn || null,
      pageCount,
      publicationDate,
      language: language || null,
      coverUrl: coverUrl || null
    };
  }

  /**
   * Extract publication year from date string
   * Handles various formats including BCE/BC years
   * @param dateString Date string in various formats
   * @returns Extracted year as string (negative for BCE) or null if invalid
   */
  private extractPublicationYear(dateString: string | null | undefined): string | null {
    if (!dateString) return null;

    // Check for BCE/BC years
    const bceMatch = dateString.match(/(\d+)\s*(?:BCE|BC)(?:\.|)/i);
    if (bceMatch && bceMatch[1]) {
      return `-${bceMatch[1]}`;
    }

    // Check for negative years (already in BCE format)
    const negativeMatch = dateString.match(/^-(\d+)$/);
    if (negativeMatch && negativeMatch[1]) {
      return dateString; // Return as-is since it's already negative
    }

    // Try to extract 4-digit year (CE/AD)
    const yearMatch = dateString.match(/\b(19|20)\d{2}\b/);
    if (yearMatch) {
      return yearMatch[0];
    }

    // Try to extract any 4-digit number that could be a year
    const anyYearMatch = dateString.match(/\b(\d{4})\b/);
    if (anyYearMatch) {
      const year = parseInt(anyYearMatch[1]);
      // Reasonable year range check (1000 CE to current year + 10)
      const currentYear = new Date().getFullYear();
      if (year >= 1000 && year <= currentYear + 10) {
        return year.toString();
      }
    }

    // If no valid year found, return null
    return null;
  }

  /**
   * Check if error is a timeout error
   * @param error Error object to check
   * @returns True if error is timeout-related
   */
  private isTimeoutError(error: any): boolean {
    return error?.code === 'ECONNABORTED' ||
           error?.message?.includes('timeout') ||
           error?.code === 'ETIMEDOUT';
  }

  /**
   * Clean up old cache entries periodically
   */
  private cleanupCache(): void {
    if (this.searchCache.size > 100) {
      const oldestEntries = Array.from(this.cacheTimestamps.entries())
        .sort(([,a], [,b]) => a - b)
        .slice(0, 50);

      oldestEntries.forEach(([key]) => {
        this.searchCache.delete(key);
        this.cacheTimestamps.delete(key);
      });

      this.logger.debug('Cleaned up old cache entries', {
        removedEntries: oldestEntries.length,
        remainingEntries: this.searchCache.size
      });
    }
  }
}
