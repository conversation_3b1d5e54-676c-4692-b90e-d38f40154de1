# SQLITE3 TO BETTER-S<PERSON>ITE3 MIGRATION PLAN

This document provides a thorough, step-by-step plan to migrate the Noti Electron application from node-sqlite3 (callback/async-based) to better-sqlite3 (synchronous, prepared-statement-based). The migration aims to improve performance, reduce complexity, and increase reliability.

## TABLE OF CONTENTS

1. [Project Scope and Overview](#project-scope-and-overview)
2. [Migration Strategy](#migration-strategy)
3. [Phase 0 — Discovery and Baseline](#phase-0--discovery-and-baseline)
4. [Phase 1 — Dependency and Environment Setup](#phase-1--dependency-and-environment-setup)
5. [Phase 2 — Core Database Layer Migration](#phase-2--core-database-layer-migration)
6. [Phase 3 — Database-API Layer Rewrite](#phase-3--database-api-layer-rewrite)
7. [Phase 4 — Timer API Rewrite](#phase-4--timer-api-rewrite)
8. [Phase 5 — Settings and Other API Modules](#phase-5--settings-and-other-api-modules)
9. [Phase 6 — Scripts and Utilities](#phase-6--scripts-and-utilities)
10. [Phase 7 — Integration, Testing, and Validation](#phase-7--integration-testing-and-validation)
11. [Phase 8 — Optimizations and Hardening](#phase-8--optimizations-and-hardening)
12. [Phase 9 — Code Consistency and Cleanup](#phase-9--code-consistency-and-cleanup)
13. [Per-File Action Checklist](#per-file-action-checklist)
14. [Risk Assessment and Mitigations](#risk-assessment-and-mitigations)
15. [Validation Plan](#validation-plan)
16. [Performance Expectations](#performance-expectations)
17. [Final Notes](#final-notes)

## PROJECT SCOPE AND OVERVIEW

### Migration Scope

**Total Files Affected**: 20+ files across the entire application

**Critical Files (3)** - Core database infrastructure:
- `electron/main/database/database.ts` - Database connection and initialization
- `electron/main/database/database-api.ts` - CRUD operations and transaction helpers
- `electron/main/database/db-test-utils.ts` - Testing utilities

**High Priority Files (2)** - Complex transaction logic:
- `electron/main/api/timer-api.ts` - Timer sessions with complex transactions
- `electron/main/api/settings-api.ts` - Application settings management

**Medium Priority Files (5)** - Direct sqlite3 usage:
- `electron/main/api/recent-items-api.ts` - Recent items tracking
- `scripts/populateTestData.ts` - Test data generation
- `scripts/populateTimerTestData.ts` - Timer test data
- `scripts/dumpDatabaseContents.ts` - Database content export
- `scripts/dumpDatabaseContents.cjs` - CommonJS version

**Low Priority Files (4)** - Import updates only:
- `electron/main/api/books-api.ts` - Uses database-api.ts helpers
- `electron/main/api/notes-api.ts` - Uses database-api.ts helpers
- `electron/main/api/folders-api.ts` - Uses database-api.ts helpers
- `electron/main/api/media-api.ts` - Uses database-api.ts helpers

**Sync Logic Files (8)** - Import updates only:
- All files in `electron/main/api/sync-logic/` directory
- Already use database-api.ts helpers, no direct sqlite3 usage

**Configuration Files (1)**:
- `package.json` - Dependency and script updates

### Migration Type
- **No schema/data migration required** - Only driver and API usage migration
- **Backward compatible** - Database files remain unchanged
- **Performance focused** - Significant speed improvements expected

### Better-SQLite3 Key Features
- **Synchronous API** - No callbacks or promises needed
- **Prepared statements** - Automatic statement caching and reuse
- **Transaction support** - `db.transaction()` with automatic rollback
- **Performance optimized** - 2-10x faster than node-sqlite3
- **TypeScript support** - Built-in type definitions
- **WAL mode support** - Better concurrency and performance
- **Backup utilities** - Built-in database backup functionality

### References
- Install better-sqlite3: `npm install better-sqlite3`
- Database constructor and options
- `db.prepare().get()/all()/run()`, `db.exec()`, `db.pragma()`, `db.transaction()`
- WAL mode, performance tips, backup, BigInt handling, unsafe mode, statement caching
- Benchmarks indicating significant improvement over node-sqlite3

## MIGRATION STRATEGY

### Sequential Phase Approach
The migration follows a carefully orchestrated sequence to minimize risk and ensure system stability:

1. **Foundation First** - Core database layer migration
2. **API Layer** - Database abstraction layer updates
3. **Application APIs** - Business logic layer migration
4. **Utilities** - Scripts and testing tools
5. **Integration** - Testing and validation
6. **Optimization** - Performance tuning and hardening

### Compatibility Strategy
- **Maintain async APIs** - Keep external function signatures async for minimal breaking changes
- **Gradual rollout** - Phase-by-phase implementation with rollback capability
- **Comprehensive testing** - Validate each phase before proceeding
- **Performance monitoring** - Track improvements throughout migration

---

# PHASE 0 — DISCOVERY AND BASELINE

Goal: Ensure full context and assess impact before changes.

Tasks:
0.1 Inventory all DB usages:
- search for "import sqlite3" and "getDatabase()" consumers across electron/main and scripts.
- Identify transaction patterns: serialize(), BEGIN/COMMIT/ROLLBACK SQL usage, custom withTransaction helpers.
- Note return types usage for RunResult (lastID, changes) to map to better-sqlite3 info object (lastInsertRowid, changes).

0.2 Establish performance baseline:
- Record existing typical operations: app startup time to ready, CRUD of notes/books, timer operations.
- Optionally use benchmark-like loops to measure n=100/1000 row inserts/selects.

0.3 Prepare rollback plan:
- Tag a git branch for the current sqlite3 implementation (e.g., branch sqlite3-baseline).
- Back up local databases: noti-database.sqlite and WAL files.

## Rollback Strategy

### Git Branch Management
```bash
# Create baseline branch before starting migration
git checkout -b sqlite3-baseline
git tag v1.0-sqlite3-stable

# Create migration branch
git checkout main
git checkout -b feature/better-sqlite3-migration
```

### Database Backup Strategy
```bash
# Backup database files before migration
cp ~/.config/noti/noti-database.sqlite ~/.config/noti/noti-database.sqlite.backup
cp ~/.config/noti/noti-database.sqlite-wal ~/.config/noti/noti-database.sqlite-wal.backup
cp ~/.config/noti/noti-database.sqlite-shm ~/.config/noti/noti-database.sqlite-shm.backup
```

### Rollback Procedure
If migration issues occur:

1. **Immediate rollback:**
   ```bash
   git checkout sqlite3-baseline
   npm install  # Restore sqlite3 dependencies
   ```

2. **Database restoration:**
   ```bash
   cp ~/.config/noti/noti-database.sqlite.backup ~/.config/noti/noti-database.sqlite
   cp ~/.config/noti/noti-database.sqlite-wal.backup ~/.config/noti/noti-database.sqlite-wal
   cp ~/.config/noti/noti-database.sqlite-shm.backup ~/.config/noti/noti-database.sqlite-shm
   ```

3. **Verify rollback:**
   - Test application startup
   - Verify all data is intact
   - Run basic CRUD operations

### Rollback Decision Criteria
Consider rollback if:
- **Critical functionality broken** after 2+ hours of debugging
- **Data corruption detected** during testing
- **Performance significantly degraded** (>20% slower)
- **Build/deployment issues** cannot be resolved quickly
- **Timeline constraints** require immediate stability



# PHASE 1 — DEPENDENCY AND ENVIRONMENT SETUP

**Goal**: Remove sqlite3, add better-sqlite3, ensure Electron build compatibility.

## Tasks

### 1.1 Dependencies Management
**Remove existing sqlite3 dependencies:**
```bash
npm uninstall sqlite3
npm uninstall @types/sqlite3  # If present in devDependencies
```

**Add better-sqlite3:**
```bash
npm install better-sqlite3
```
- Types are bundled with better-sqlite3; @types package not required
- Verify TypeScript usage works with built-in definitions

### 1.2 Package.json Script Updates ⚠️ **CRITICAL ADDITION**
**Update build and rebuild scripts** (addresses identified gap):

**Current scripts that need updating:**
```json
{
  "scripts": {
    "postinstall": "electron-rebuild -f -w sqlite3",
    "electron-rebuild": "electron-rebuild -f -w sqlite3"
  }
}
```

**Updated scripts:**
```json
{
  "scripts": {
    "postinstall": "electron-rebuild -f -w better-sqlite3",
    "electron-rebuild": "electron-rebuild -f -w better-sqlite3"
  }
}
```

**Rationale**: Electron-rebuild needs to compile native modules for the Electron runtime. Failing to update these scripts will cause build failures.

### 1.3 Electron Build Verification
**Ensure electron-builder compatibility:**
- Verify electron-builder bundles native better-sqlite3 binaries correctly
- For Windows: Ensure Node.js native tools are installed if compilation required
- Test installation: `node -e "console.log(require('better-sqlite3'))"`

**Build verification steps:**
1. Clean install: `rm -rf node_modules package-lock.json && npm install`
2. Rebuild native modules: `npm run electron-rebuild`
3. Test import: `node -e "const db = require('better-sqlite3')(':memory:'); console.log('Success')"`
4. Build application: `npm run build`

### 1.4 Cross-platform Considerations
**Primary platform**: Windows 11 (verified working)
**Secondary platforms**: macOS, Linux (optional verification)

**Special considerations:**
- If custom SQLite build needed (rare), consider build-from-source flags
- Review `nativeBinding` option if standard binaries don't work
- Test on target deployment platforms early in migration

### 1.5 Development Environment Setup
**Recommended development workflow:**
1. Create feature branch: `git checkout -b feature/better-sqlite3-migration`
2. Tag current state: `git tag sqlite3-baseline`
3. Set up parallel testing environment
4. Configure IDE for better-sqlite3 TypeScript definitions



---

# PHASE 2 — CORE DATABASE LAYER MIGRATION

**Files:**
- `electron/main/database/database.ts` (CRITICAL)
- `electron/main/database/database-hooks.ts` (integration review required)

**Complexity**: HIGH - Core infrastructure changes affecting entire application

## 2.1 Replace sqlite3 Import and Types

**Current implementation:**
```typescript
import sqlite3 from 'sqlite3';
import { app } from 'electron';
// ...
type Database = sqlite3.Database;
```

**New implementation:**
```typescript
import Database from 'better-sqlite3';
import { app } from 'electron';
// ...
type DatabaseInstance = Database;  // Database is constructor, instances have this type
```

**Key differences:**
- `Database` is now the constructor function, not a type
- Instance type is the return type of `new Database()`
- No need for separate type imports

## 2.2 Connection Lifecycle and Singleton

**Current callback-based initialization:**
```typescript
const db: Database = new sqlite3.Database(dbPath, async (err: Error | null) => {
  if (err) {
    console.error('Error opening database:', err.message);
    reject(err);
    return;
  }
  // ... async setup
});
```

**New synchronous initialization:**
```typescript
const db = new Database(dbPath, {
  readonly: false,
  fileMustExist: false,
  timeout: 10000,
  verbose: process.env.NODE_ENV === 'development' ? console.log : null
});
```

**Updated singleton pattern:**
```typescript
let dbInstance: DatabaseInstance | null = null;

export const getDatabase = (): DatabaseInstance => {
  if (!dbInstance) {
    dbInstance = initDatabase(); // Now synchronous
  }
  return dbInstance;
};

export const closeDatabase = (): void => {
  if (dbInstance) {
    dbInstance.close();
    dbInstance = null;
  }
};
```

**⚠️ Critical Addition - getDatabase() Fallback Path:**
The current `getDatabase()` function has a fallback creation path that also needs migration:
```typescript
// Current fallback (lines 469-477 in database.ts)
export const getDatabase = (): Database => {
  if (dbInstance) {
    return dbInstance;
  }

  const dbPath = getDbPath();
  dbInstance = new sqlite3.Database(dbPath, (err: Error | null) => {
    if (err) {
      console.error('Error connecting to database:', err.message);
      throw err;
    }
  });

  return dbInstance;
};
```

**Updated fallback:**
```typescript
export const getDatabase = (): DatabaseInstance => {
  if (dbInstance) {
    return dbInstance;
  }

  const dbPath = getDbPath();
  dbInstance = new Database(dbPath, {
    readonly: false,
    fileMustExist: true, // Database should exist by this point
    timeout: 10000
  });

  return dbInstance;
};
```

2.3 Initialization flow becomes synchronous
- Old setup functions use runAsync/getAsync helpers and await.
- New setup should use db.exec() and db.pragma(). No async/await needed unless preserving API shape.

Suggested structure:
- getUserDataPath()/ensureDataDirectory()/getDbPath() unchanged.
- setupDatabaseConfig(db):
  - db.pragma('busy_timeout = 10000');  // or use Database option timeout
  - db.pragma('journal_mode = WAL');
  - db.pragma('foreign_keys = ON');
- createAllTables(db):
  - Use db.exec(multi-statement SQL) or sequential db.exec() per table. Keep same schema.
- handleDatabaseMigrations(db):
  - Use try/catch blocks around db.exec('ALTER TABLE ...') similar to previous, but synchronous. 
  - Note: SQLite ALTER DROP COLUMN isn’t supported in older versions. The old code attempted DROP COLUMN and tolerated failures; keep the logic but expect exceptions. If DROP COLUMN is not supported, silently continue as previous code does.
- createDatabaseIndexes(db):
  - db.exec('CREATE INDEX IF NOT EXISTS ...');
- setupDefaultData(db):
  - Use prepare().get() and prepare().run() for SELECT/INSERT.

## 2.4 Critical Pragma and Transaction Sequencing

**⚠️ CRITICAL**: Execute pragmas BEFORE transactions (pragmas cannot run inside transactions):

```typescript
const initDatabase = (): DatabaseInstance => {
  const db = new Database(dbPath, options);

  // Execute pragmas FIRST, outside any transaction
  db.pragma('busy_timeout = 10000');
  db.pragma('journal_mode = WAL');
  db.pragma('foreign_keys = ON');

  // Then wrap schema operations in transaction
  const setup = db.transaction(() => {
    createAllTables(db);
    handleDatabaseMigrations(db);
    createDatabaseIndexes(db);
    setupDefaultData(db);
  });
  setup();

  return db;
};
```

**Why this sequence matters:**
1. **Pragmas must be outside transactions** - SQLite restriction
2. **Schema operations benefit from transactions** - Atomic setup
3. **WAL mode enables better concurrency** - Must be set early
4. **Foreign keys must be enabled** - Before any data operations

## 2.5 Remove Async Helpers

**Functions to remove:**
```typescript
// Remove these helper functions (lines 12-29 in database.ts)
const runAsync = (db: Database, sql: string): Promise<void> => { /* ... */ };
const getAsync = (db: Database, sql: string): Promise<any> => { /* ... */ };
```

**Replacement pattern:**
```typescript
// Old: await runAsync(db, 'CREATE TABLE ...')
// New: db.exec('CREATE TABLE ...')

// Old: await getAsync(db, 'SELECT ...')
// New: db.prepare('SELECT ...').get()
```

## 2.6 Error Handling, Logging, and Hooks

**Maintain initialization logs:**
```typescript
console.log(`Initializing database at: ${dbPath}`);
console.log('Connected to the SQLite database.');
console.log('WAL mode enabled successfully');
console.log('Foreign key support enabled.');
```

**Database hooks compatibility:**
- `databaseHooks.initialize()` and `databaseHooks.shutdown()` calls remain unchanged
- Hooks system is event-based and doesn't assume callback-style DB access
- Verify hooks don't expect async database operations

**Error handling patterns:**
```typescript
// Maintain existing error handling structure
try {
  setupDatabase(db);
  dbInstance = db;
  console.log('Database singleton instance assigned.');
} catch (setupError) {
  console.error('Error setting up database:', setupError);
  throw setupError;
}
```

## 2.7 PRAGMA Compatibility

**Use better-sqlite3 pragma method:**
```typescript
// Old: await runAsync(db, 'PRAGMA journal_mode = WAL');
// New: db.pragma('journal_mode = WAL');

// Return value handling (optional)
const walMode = db.pragma('journal_mode = WAL');
console.log('WAL mode result:', walMode); // Returns 'wal' if successful
```

**Supported pragma calls:**
- `db.pragma('busy_timeout = 10000')`
- `db.pragma('journal_mode = WAL')`
- `db.pragma('foreign_keys = ON')`
- `db.pragma('cache_size = 32000')` (optimization)

Deliverables:
- database.ts fully migrated to synchronous initialization, singleton connection, pragmas, schema creation, migrations, default data and indexes inside a single transaction.
- No external API change for getDatabase() if possible (but now returns BetterDatabase).



---

# PHASE 3 — DATABASE-API LAYER REWRITE

**File:** `electron/main/database/database-api.ts` (CRITICAL)

**Complexity:** HIGH - Central database abstraction layer used by entire application

**Impact:** All API modules depend on this layer - changes affect entire codebase

## 3.1 Imports and Types

**Current imports:**
```typescript
import { getDatabase } from './database';
import sqlite3 from 'sqlite3';
import * as fs from 'fs';

// Types
type Database = sqlite3.Database;
type RunResult = sqlite3.RunResult;
```

**New imports:**
```typescript
import { getDatabase } from './database';
import type Database from 'better-sqlite3';
import * as fs from 'fs';

// Types - better-sqlite3 provides these built-in
// No need for separate RunResult type - use return type directly
```

**Type changes:**
- Remove `sqlite3.Database` and `sqlite3.RunResult` references
- Use better-sqlite3's built-in TypeScript definitions
- Update function signatures to match new return types

3.2 Replace helper functions dbGet/dbAll/dbRun
- Old async Promise-based helpers wrap callbacks. Replace with sync:

  const dbGet = &lt;T&gt;(query: string, params: any[] = []): T | undefined =&gt; {
    const db = getDatabase();
    const stmt = db.prepare(query);
    return stmt.get(...params) as T | undefined;
  };

  const dbAll = &lt;T&gt;(query: string, params: any[] = []): T[] =&gt; {
    const db = getDatabase();
    const stmt = db.prepare(query);
    return stmt.all(...params) as T[];
  };

  const dbRun = (query: string, params: any[] = []): { changes: number; lastInsertRowid: number } =&gt; {
    const db = getDatabase();
    const stmt = db.prepare(query);
    const info = stmt.run(...params);
    return { changes: info.changes, lastInsertRowid: Number(info.lastInsertRowid) };
  };

- If external modules expect RunResult.lastID, provide a compatibility wrapper:
  type RunInfoCompat = { changes: number; lastID: number };
  const dbRunCompat = (...) =&gt; {
    const info = stmt.run(...params);
    return { changes: info.changes, lastID: Number(info.lastInsertRowid) };
  };
- Update all consumers in this file to use new return shape (prefer migrating to lastInsertRowid; if keeping .lastID internally, translate immediately after run()).

3.3 Update CRUD functions to sync pattern
- createNote/createFolder/createBook: 
  - After INSERT, retrieve new row via get by lastInsertRowid.
- get...(): directly return dbGet/dbAll result. Add explicit not-found checks when prior code expected throws.
- update...(): run UPDATE, check changes, then fetch updated entity.
- delete...(): run DELETE, return success based on changes &gt; 0.

3.4 Transactions
- Rewrite withTransaction and withReadTransaction using db.transaction():
  const withTransaction = &lt;T&gt;(operation: () =&gt; T): T =&gt; {
    const db = getDatabase();
    const tx = db.transaction(operation);
    return tx();
  };

- For read transaction semantics (consistent snapshot), consider tx = db.transaction(operation) and call tx.immediate() or deferred() depending on previous behavior (BEGIN IMMEDIATE TRANSACTION previously used in withReadTransaction). Mirror with .immediate() for close fidelity:
  const withReadTransaction = &lt;T&gt;(operation: () =&gt; T): T =&gt; {
    const db = getDatabase();
    return db.transaction(operation).immediate();
  };

3.5 deleteBook special case
- Old version used db.serialize(), BEGIN/COMMIT, file deletion, and multiple queries. Convert to:
  - Use db.transaction((id: number) =&gt; { ... }) returning { success, id }.
  - Inside transaction:
    - Query media files for the book (SELECT ...).
    - Delete DB records for media (DELETE ...).
    - Defer filesystem deletions:
      - Best practice: either perform fs.unlinkSync inside transaction cautiously (risk if unlink throws).
      - Safer: gather file paths inside transaction, perform fs operations after transaction successfully commits; if unlink fails, log warnings.
    - Delete book row, then commit by returning.
  - If the old code relied on ON DELETE SET NULL for notes.book_id, keep behavior.

3.6 Statement caching (optional optimization)
- Implement a small cache for hot queries:
  const cache = new Map&lt;string, Database.Statement&gt;();
  function prepareCached(sql: string) { if (!cache.has(sql)) cache.set(sql, getDatabase().prepare(sql)); return cache.get(sql)!; }

- Use cache for frequent SELECTs in list retrieval or hot paths.

Deliverables:
- database-api.ts fully synchronous, with replaced helpers, transactions via db.transaction, and compatibility handling for lastInsertRowid.



PHASE 4 — TIMER API REWRITE

File: electron/main/api/timer-api.ts (HIGH, complex transaction logic)

4.1 Imports/types
- Remove sqlite3 imports and type alias Database = sqlite3.Database.
- Import getDatabase and, optionally, local helpers similar to database-api.ts or reuse dbGet/dbAll/dbRun from that module if exported. Prefer reusing database-api helpers to centralize DB calls.

4.2 Replace local helpers dbGet/dbAll/dbRun
- Use shared helpers from database-api.ts or reimplement as sync wrappers here:
  - dbGet&lt;T&gt;(): stmt.get(...params)
  - dbAll&lt;T&gt;(): stmt.all(...params)
  - dbRun(): stmt.run(...params) returning { changes, lastInsertRowid }

4.3 Rewrite all callsites to drop async/await when not needed
- Many functions can be synchronous now. However, maintain async function signatures to avoid API churn if the rest of the app expects Promises. Implementation can return Promise.resolve(value) for compatibility, or leave them as sync and update callers. Safer approach: keep async signatures but run sync logic inside try/catch and return values.

Recommended pattern:
- Keep exported API signatures as async to avoid widespread changes.
- Internally execute synchronous better-sqlite3 calls; simply return values.

4.4 Transaction-heavy function: completePomodoroInSession
- Convert manual BEGIN/COMMIT with serialize() into db.transaction():
  const db = getDatabase();
  const fn = db.transaction((sessionId: number, cycleId: number) =&gt; {
    const updateCycle = db.prepare(...).run(cycleId, sessionId);
    if (!updateCycle.changes) throw new Error(...);

    const cycleType = db.prepare('SELECT cycle_type FROM pomodoro_cycles WHERE id = ?').get(cycleId)?.cycle_type;
    if (cycleType === 'pomodoro') {
      db.prepare('UPDATE timer_sessions SET pomodoro_cycles_completed = pomodoro_cycles_completed + 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?').run(sessionId);
    }
    const completed = db.prepare('SELECT * FROM pomodoro_cycles WHERE id = ?').get(cycleId);
    if (!completed) throw new Error('Failed to retrieve completed cycle');
    return completed;
  });
  return fn(sessionId, cycleId);

4.5 Other functions
- createUserSession, endUserSession, updateSession, startPomodoroInSession, getActiveCycleForSession, cancelActiveCycle, startTimerSession, endTimerSession, getTimerSession, deleteTimerSession, getTimerSessionsByDateRange, getTodayTimerSessions, getTimerStatsByDateRange, getTimerSettings, updateTimerSettings, resetTimerSettings, syncAllSessionPomodoroCounts
- For each:
  - Replace dbRun/dbGet/dbAll usage with synchronous equivalents.
  - Where prior logic retried on "Database is still initializing", this is likely unnecessary now due to sync init and singleton connection. Remove retry code. If any race persists at app startup, ensure getDatabase() is invoked after initDatabase() is called early, or lazily init as in Phase 2.
  - Preserve ISO timestamp usage and SQL for computing durations.
  - Preserve existing validation, but streamline with sync operations.

4.6 Consistency and snapshot reads
- With better-sqlite3, wrap multi-query read sequences requiring a consistent view using db.transaction().immediate() if needed (replacing prior "BEGIN IMMEDIATE TRANSACTION"). For simple reads, single statements suffice.

Deliverables:
- timer-api.ts uses synchronous better-sqlite3 calls, with critical transactions converted to db.transaction and async signatures retained for compatibility.



PHASE 5 — SETTINGS AND OTHER API MODULES

Files:
- electron/main/api/settings-api.ts (High)
- electron/main/api/recent-items-api.ts (Medium)
- Medium priority API modules (8 files listed by user): books-api/books-api.ts, notes-api.ts, folders-api.ts, media-api.ts, etc.

5.1 Common updates across modules
- Replace sqlite3 imports with shared database-api helpers.
- Drop callback-based style.
- Ensure update and delete functions check .changes.
- Ensure create functions use lastInsertRowid to fetch created records.
- Where multiple statements need atomicity, wrap with db.transaction().

5.2 books-api.ts specific
- If it previously imported sqlite3 directly, update to use database-api.ts wrappers.
- Any logic that depended on RunResult.lastID should be mapped to lastInsertRowid, using the compatibility wrapper if you kept it in database-api.ts.

5.3 notes-api.ts/folders-api.ts/media-api.ts
- Update all CRUD operations to synchronous patterns.

5.4 recent-items-api.ts
- Likely only queries; update to use db.prepare().all()/get().

5.5 ipc-handlers.ts
- No changes expected if it already uses async/await around API calls. Validate that synchronous operations don’t block main thread too long; better-sqlite3 is extremely fast. Keep heavy batch operations minimal or consider splitting long-running maintenance tasks.

Deliverables:
- All API modules migrated to better-sqlite3, using shared helpers and transactions where necessary.



PHASE 6 — SCRIPTS AND UTILITIES

Files:
- scripts/populateTestData.ts (Medium)
- scripts/dumpDatabaseContents.ts (Low)
- scripts/populateTimerTestData.ts (Medium)

6.1 populateTestData.ts
- Replace sqlite3 import and Database type.
- Replace initDatabase callback flow with synchronous:
  - const db = new Database(dbPath)
  - db.pragma('foreign_keys = ON');
  - Create schema via db.exec() if resetting DB. If using existing, skip schema creation.
- Replace createFolder/createNote to sync:
  const stmt = db.prepare('INSERT ...'); const info = stmt.run(...); return { id: Number(info.lastInsertRowid), name/title };
- Iterate and insert without Promises. Keep logging.
- Finalize with db.close() and log close.

6.2 dumpDatabaseContents.ts
- Replace reading logic with db.prepare(...).all() and console.log outputs.

6.3 populateTimerTestData.ts
- Mirror populateTestData.ts approach for timer-related schema and inserts.

Deliverables:
- Scripts are synchronous and simpler, maintain CLI arguments and logging semantics.



PHASE 7 — INTEGRATION, TESTING, AND VALIDATION

7.1 Build and run
- Rebuild the app. Ensure better-sqlite3 native module loads under Electron runtime.
- Launch Noti; verify database initialization logs and WAL mode activation.

7.2 Functional testing
- Database initialization on fresh profile: tables created, default data created.
- CRUD operations for notes, folders, books.
- Timer flows:
  - Start/End user session, start/complete pomodoro, stats calculation, settings changes.
- Recent items and media relations.

7.3 Transaction integrity
- Force errors inside transaction to confirm automatic rollback by db.transaction (throwing should rollback).
- End-to-end multi-step operations retain atomicity.

7.4 Performance benchmarking
- Use rough timings to compare:
  - Insertion of N notes/folders (batched within a transaction).
  - Bulk SELECT .all() with varying cardinalities.
- Expect major improvements vs sqlite3.

7.5 Cross-platform validation
- Windows 11 (primary): Ok.
- Optional: macOS, Linux packaging tests.

7.6 WAL checkpoint policy (optional)
- Consider periodic checkpoint to avoid WAL growth if app has many long-lived readers:
  - Implement optional interval to monitor db-wal size and run db.pragma('wal_checkpoint(RESTART)') if exceeds threshold.

Deliverables:
- Confirmed app behavior and improved performance metrics.



PHASE 8 — OPTIMIZATIONS AND HARDENING

8.1 Pragmas tailored for performance
- db.pragma('journal_mode = WAL');
- db.pragma('cache_size = 32000'); // Example from docs; tune based on memory/dataset
- db.pragma('foreign_keys = ON');

8.2 Statement caching
- Implement a small LRU cache or simple Map cache for hot queries. Reuse prepared statements wherever loops execute repeated operations.

8.3 BigInt handling (if needed)
- If IDs or counters might exceed Number safe range (unlikely here), consider:
  - db.defaultSafeIntegers(true) globally.
  - Or stmt.safeIntegers(true) for specific queries.

8.4 Unsafe mode review
- Avoid enabling unsafe mode unless profiling indicates a need and risk is understood:
  - db.unsafeMode(true/false) toggles constraints; keep OFF by default.

8.5 Backup support
- Offer db.backup('path') integration point for export/backup features.

8.6 Error handling and observability
- Wrap DB calls at boundaries with try/catch; log SqliteError.code for context.
- Provide health checks: simple SELECT 1 helper to detect database availability.

Deliverables:
- Tuned pragmas, optional statement caching, clear error logs, optional backup utilities.



PHASE 9 — CODE CONSISTENCY AND CLEANUP

9.1 Remove dead code
- Delete callback/Promise wrappers that are no longer used (e.g., runAsync/getAsync).
- Simplify module exports and type aliases.

9.2 Type alignment
- Align types across modules: replace legacy RunResult with unified { changes, lastInsertRowid } internally, provide local translation to .lastID for minimal surface changes if needed.

9.3 Documentation updates
- Add this document to repo.
- Provide short migration summary in CHANGELOG.md.
- Inline comments around critical transaction blocks to explain better-sqlite3 idioms.



PER-FILE ACTION CHECKLIST

CRITICAL
- electron/main/database/database.ts
  - Replace sqlite3 with better-sqlite3.
  - Sync init and schema creation via db.exec.
  - Pragmas via db.pragma; WAL + foreign_keys + timeout.
  - Wrap full setup in db.transaction().
  - Update singleton, close flow, and hooks lifecycle.

- electron/main/database/database-api.ts
  - Replace async helper wrappers with sync prepared statements.
  - Provide dbGet/dbAll/dbRun (and optional compat for lastID).
  - Rewrite transaction helpers using db.transaction().
  - Update deleteBook to transaction and externalize fs deletes post-commit.

- electron/main/database/db-test-utils.ts
  - Rewrite tests/utilities to sync operations.
  - Replace serialize and callbacks with direct exec/prepare.

HIGH
- electron/main/api/timer-api.ts
  - Replace local async helpers.
  - Convert completePomodoroInSession to db.transaction.
  - Keep external API async but internally sync.
  - Remove “Database is still initializing” retry logic.

- electron/main/api/settings-api.ts
  - Replace sqlite3 usage with shared database-api helpers.
  - Migrate to sync; transactions for multi-statement updates.

MEDIUM
- electron/main/api/recent-items-api.ts
  - Update imports and use db.prepare().get()/all()/run().

Medium Priority — API Modules (8 files)
- electron/main/api/books-api/books-api.ts
- electron/main/api/notes-api.ts
- electron/main/api/folders-api.ts
- electron/main/api/media-api.ts
- (plus others identified in repo)
  Actions: Update imports to database-api.ts helpers, convert to sync, ensure transactions where needed.

LOW
- electron/main/ipc-handlers.ts
  - Likely unchanged; verify compatibility with sync operations.

Sync Logic Files (8 files)
- Update imports only if referencing sqlite3 directly.

Scripts
- scripts/populateTestData.ts (Medium)
  - Rewrite: init DB via new Database(), pragma on, schema via exec, inserts via prepare().run().
- scripts/dumpDatabaseContents.ts (Low)
  - Read using prepare().all() and log.
- scripts/populateTimerTestData.ts (Medium)
  - Same as populateTestData for timer tables.



RISK ASSESSMENT AND MITIGATIONS

- Risk: Long operations in main thread due to synchronous DB calls.
  Mitigation: better-sqlite3 is very fast; for extreme operations, use transactions and batching; if needed, offload bulk scripts to CLI scripts outside Electron main process.

- Risk: Transaction logic parity.
  Mitigation: Explicitly convert all BEGIN/COMMIT/ROLLBACK flows into db.transaction() with proper error throwing to ensure automatic rollback, mirroring previous semantics.

- Risk: Type mismatches (.lastID vs lastInsertRowid).
  Mitigation: Provide a small compatibility layer returning lastID from lastInsertRowid where needed.

- Risk: WAL file growth under constant readers.
  Mitigation: Optional interval-based checkpoint using db.pragma('wal_checkpoint(RESTART)') when -wal file exceeds threshold.

- Risk: DROP COLUMN in migrations not supported.
  Mitigation: Maintain try/catch logging and tolerance as old code did; ensure migrations don’t block init.

- Risk: Electron packaging of native module.
  Mitigation: Test build pipeline early after Phase 1 on target OS; confirm module loads at runtime.



VALIDATION PLAN

## Testing Strategy

### Unit Tests
- **Update existing tests** for database-api and timer-api critical paths
- **Add new tests** for better-sqlite3 specific functionality
- **Test transaction behavior** - ensure rollback works correctly
- **Test prepared statement caching** if implemented
- **Verify error handling** matches previous behavior

### Integration Tests
- **End-to-end sanity**: Launch app, create/update/delete notes, folders, and books
- **Timer functionality**: Create sessions, pomodoro completion, validate counters and stats
- **Sync operations**: Full sync cycle testing with new database layer
- **Settings management**: Verify all settings operations work correctly

### Performance Testing
- **Benchmark critical operations** before/after migration
- **Log performance improvements** for documentation
- **Test with large datasets** to verify scalability improvements
- **Monitor memory usage** during extended operations

### Cross-Platform Testing
- **Windows 11** (primary platform) - comprehensive testing
- **macOS/Linux** (optional) - smoke tests for build compatibility

## Troubleshooting Guide

### Common Migration Issues

**Issue: "Cannot find module 'better-sqlite3'"**
- Solution: Ensure `npm install better-sqlite3` completed successfully
- Check: Verify native module compilation with `npm run electron-rebuild`

**Issue: "Database file is locked"**
- Solution: Ensure all sqlite3 connections are properly closed before migration
- Check: Look for lingering database connections in development tools

**Issue: "PRAGMA statements fail"**
- Solution: Verify pragmas are executed outside of transactions
- Check: Ensure pragma syntax matches better-sqlite3 format

**Issue: "Transaction rollback not working"**
- Solution: Ensure errors are thrown (not returned) within transaction functions
- Check: Verify transaction function structure matches better-sqlite3 patterns

**Issue: "Performance not improved"**
- Solution: Verify prepared statements are being used correctly
- Check: Implement statement caching for frequently used queries

**Issue: "Type errors with lastID"**
- Solution: Use compatibility wrapper or update to lastInsertRowid
- Check: Verify all RunResult.lastID references are updated

### Debugging Tips

1. **Enable verbose logging** during development:
   ```typescript
   const db = new Database(dbPath, { verbose: console.log });
   ```

2. **Test database operations in isolation** before full integration

3. **Use better-sqlite3's built-in error messages** - they're more descriptive than sqlite3

4. **Monitor WAL file growth** during development and testing

5. **Verify foreign key constraints** are working as expected



PERFORMANCE EXPECTATIONS

## Expected Performance Improvements

### Quantitative Benchmarks
Based on better-sqlite3 documentation and community benchmarks:

**Read Operations:**
- Simple SELECT queries: 2-3x faster
- Complex JOIN queries: 1.5-2x faster
- Bulk SELECT operations: 3-5x faster

**Write Operations:**
- Single INSERT: 2-4x faster
- Bulk INSERT (in transaction): 5-10x faster
- UPDATE operations: 2-3x faster
- DELETE operations: 2-3x faster

**Transaction Performance:**
- Transaction overhead: 10-20x faster (no callback overhead)
- Nested transaction logic: Significantly simplified
- Error handling: More predictable and faster

### Application-Specific Improvements

**Database Initialization:**
- Current: ~500-1000ms (callback overhead)
- Expected: ~100-200ms (synchronous)

**Timer Operations:**
- `completePomodoroInSession`: 50-70% faster (complex transaction)
- Session queries: 2-3x faster
- Statistics calculations: 3-5x faster

**Note/Book CRUD:**
- Note creation: 2-3x faster
- Bulk operations: 5-10x faster
- Search operations: 2-4x faster

**Sync Operations:**
- Manifest generation: 3-5x faster
- Change detection: 2-3x faster
- Bulk sync operations: 5-10x faster

### Memory Usage
- **Lower memory footprint**: No callback queue overhead
- **Better garbage collection**: Fewer temporary objects
- **Prepared statement caching**: Reduced parsing overhead

---

# FINAL NOTES

## Implementation Guidelines

- **Maintain async exported APIs** for minimal surface churn, even though internals are now sync and immediate
- **Centralize all DB access** through database-api.ts to standardize patterns and facilitate future changes
- **Keep pragmas explicit and logged** for easier diagnostics
- **Implement lightweight statement cache** for hottest SELECTs/INSERTs
- **Monitor performance improvements** throughout migration process

## Success Criteria

1. **Functional Parity**: All existing functionality works identically
2. **Performance Gains**: Measurable improvements in database operations
3. **Code Simplification**: Reduced complexity in transaction handling
4. **Stability**: No regressions in reliability or error handling
5. **Maintainability**: Cleaner, more readable database code

## Post-Migration Opportunities

1. **Advanced Optimizations**: Statement caching, connection pooling
2. **New Features**: Database backup/restore, advanced analytics
3. **Performance Monitoring**: Built-in query performance tracking
4. **Error Handling**: Enhanced error reporting and recovery

This comprehensive plan enumerates every file requiring changes, outlines exact code-level transformations, and details testing/validation to ensure a robust transition to better-sqlite3 with significant performance gains.
