import axios, { AxiosError, AxiosRequestConfig } from 'axios';
import logger from './logger';

const httpClient = axios.create({
  timeout: 25000, // Default timeout
  maxRedirects: 5,
  // Enforce transport-level response caps to avoid downloading oversized images/files
  // 10MB max content length should align with image policy; maxBodyLength is for Node adapters
  maxContentLength: 10 * 1024 * 1024,
  maxBodyLength: 10 * 1024 * 1024,
  headers: {
    'Book-Search-NoteTakingApp': 'Noti/1.0 (https://github.com/noti-app/noti)',
  },
});

// Retry logic for network failures
const retryRequest = async (error: AxiosError, retryCount = 0): Promise<any> => {
  // Maximum retry attempts
  const maxRetries = 3;
  
  // If we've reached max retries, reject the promise
  if (retryCount >= maxRetries) {
    logger.error(`Max retries reached for request: ${error.config?.url}`);
    return Promise.reject(error);
  }
  
  // Retry on network timeouts or connection errors ONLY for idempotent requests
  const method = (error.config?.method || 'get').toUpperCase();
  const isIdempotent = method === 'GET' || method === 'HEAD';
  if (isIdempotent && (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT' || !error.response)) {
    const delay = 1000 * Math.pow(2, retryCount); // Exponential backoff
    logger.debug(`Retrying request (attempt ${retryCount + 1}/${maxRetries}) after ${delay}ms delay`);
    
    // Wait for the delay
    await new Promise(resolve => setTimeout(resolve, delay));
    
    // Retry the request with incremented retry count
    return httpClient.request(error.config as AxiosRequestConfig).catch(err => retryRequest(err, retryCount + 1));
  }
  
  // For other errors or non-idempotent methods, don't retry
  return Promise.reject(error);
};

// Request Interceptor for Logging
httpClient.interceptors.request.use(request => {
  logger.debug(`--> ${request.method?.toUpperCase()} ${request.url}`);
  return request;
}, error => {
  logger.error(`REQUEST ERROR: ${error.message}`);
  return Promise.reject(error);
});

// Response Interceptor for Logging
httpClient.interceptors.response.use(response => {
  logger.debug(`<-- ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`);
  return response;
}, async error => {
  if (axios.isAxiosError(error) && error.response) {
    logger.error(`<-- ${error.response.status} ${error.config.method?.toUpperCase()} ${error.config.url}`);
  } else {
    logger.error(`RESPONSE ERROR: ${error.message}`);
  }
  
  // Implement retry logic for network failures
  return retryRequest(error);
});

export default httpClient;
