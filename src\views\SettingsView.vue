<template>
    <div class="settings-view">
        <div class="settings-content">
            <UserSettings />
            <ThemeSettings />
            <ClockSettings />
            <DiscordSettings />
        </div>
    </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import UserSettings from '../components/settings/UserSettings.vue'
import ThemeSettings from '../components/settings/ThemeSettings.vue'
import ClockSettings from '../components/settings/ClockSettings.vue'
import DiscordSettings from '../components/settings/DiscordSettings.vue'
import { useDiscordActivity } from '../composables/useDiscordActivity'
import { usePageLoadMonitoring } from '../composables/usePageLoadMonitoring'

const { setSettingsActivity } = useDiscordActivity()

// Page load monitoring
const { autoRecordPageMounted } = usePageLoadMonitoring()
autoRecordPageMounted('Settings')

// Set Discord activity when user enters settings
onMounted(() => {
  setSettingsActivity()
})
</script>

<style scoped>
.settings-view {
    padding: 20px;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}


.settings-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}
</style>