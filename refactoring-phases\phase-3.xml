<?xml version="1.0" encoding="UTF-8"?>
<refactoring-phase id="3" name="Service Integration & Modernization">
  <metadata>
    <estimated-duration>4 days</estimated-duration>
    <risk-level>medium</risk-level>
    <dependencies>
      <dependency type="phase">phase-2</dependency>
      <dependency type="service">OpenLibraryService from Phase 2</dependency>
      <dependency type="service">CoverService from Phase 2</dependency>
      <dependency type="file">electron/main/api/books-api.ts</dependency>
    </dependencies>
    <next-phase>phase-4</next-phase>
  </metadata>

  <objectives>
    <primary-goal>Complete the service extraction by implementing complex integration services and orchestration logic, while modernizing the most challenging aspects of the monolithic books-api.ts</primary-goal>
    <secondary-goals>
      <goal>Replace custom fuzzy search logic with Fuse.js implementation</goal>
      <goal>Extract and modernize core CRUD operations with Zod validation</goal>
      <goal>Implement service orchestration with proper dependency injection</goal>
      <goal>Delete legacy custom search algorithms and replace with industry-standard library</goal>
      <goal>Clean up monolithic file by removing extracted functionality</goal>
    </secondary-goals>
  </objectives>

  <milestones>
    <milestone id="3.1" name="Search Service Modernized">
      <description>Custom search logic is completely replaced with Fuse.js implementation and integrated with OpenLibraryService</description>
      <validation>Search results are accurate, performant, and use modern fuzzy search algorithms</validation>
    </milestone>
    <milestone id="3.2" name="Book Service Orchestrated">
      <description>Core business logic orchestrates all services with comprehensive Zod validation</description>
      <validation>All CRUD operations use proper validation, service delegation, and error handling</validation>
    </milestone>
    <milestone id="3.3" name="Legacy Code Eliminated">
      <description>Monolithic books-api.ts is cleaned up with extracted functions removed or commented</description>
      <validation>File is significantly reduced in size and complexity while maintaining compilation</validation>
    </milestone>
  </milestones>

  <tasks>
    <task id="3.1" priority="critical" estimated-time="10 hours">
      <name>Extract Search Service</name>
      <description>Replace custom search logic with Fuse.js implementation and integrate with OpenLibraryService for hybrid search functionality</description>
      <files-to-create>
        <file>electron/main/api/books-api/services/search.service.ts</file>
      </files-to-create>
      <files-to-modify>
        <file>electron/main/api/books-api.ts</file>
      </files-to-modify>
      <codebase-analysis>
        <current-location>
          <function name="calculateEnhancedRelevanceScore" lines="369-525" complexity="very-high">
            <description>Complex relevance scoring algorithm with fuzzy matching, word analysis, and deduplication</description>
            <dependencies>getEnhancedStringSimilarity, multiple helper functions, local books comparison</dependencies>
            <current-issues>Over 150 lines of custom logic, difficult to maintain, no configurability</current-issues>
          </function>
          <function name="getEnhancedStringSimilarity" lines="244-358" complexity="high">
            <description>Custom string similarity algorithm with edit distance, position weighting, and substring matching</description>
            <dependencies>getEditDistance, normalizeString, multiple helper functions</dependencies>
            <current-issues>100+ lines of custom algorithm, reinventing established solutions</current-issues>
          </function>
          <function name="searchBooksHybrid" lines="1310-1350" complexity="medium">
            <description>Orchestrates local and online search with result merging</description>
            <dependencies>searchBooksInDb, searchBooksOnline, result processing</dependencies>
            <current-issues>Simple orchestration but depends on complex custom scoring</current-issues>
          </function>
          <helper-functions>
            <function name="getEditDistance" lines="189-236" description="Levenshtein distance calculation"/>
            <function name="normalizeString" lines="237-243" description="String normalization for comparison"/>
            <function name="preprocessQuery" lines="526-531" description="Query preprocessing for better matching"/>
          </helper-functions>
        </current-location>
      </codebase-analysis>
      <implementation-details>
        <step>Analyze existing search logic in books-api.ts (lines 244-525 and 1310-1350)</step>
        <step>Create SearchService class with dependency injection constructor</step>
        <step>COMPLETELY DELETE custom functions: calculateEnhancedRelevanceScore, getEnhancedStringSimilarity, getEditDistance, normalizeString</step>
        <step>Implement Fuse.js-based local search with configurable options</step>
        <step>Extract and refactor searchBooksHybrid logic with service integration</step>
        <step>Implement intelligent result merging and ranking using Fuse.js scores</step>
        <step>Add dependency injection for OpenLibraryService from Phase 2</step>
        <step>Replace all custom scoring with Fuse.js relevance scores</step>
        <step>Preserve query preprocessing and ISBN detection logic</step>
        <step>Preserve legacy typo-correction behavior in query preprocessing (e.g., teh→the, adn→and, fo→of) to maintain result parity</step>
        <step>Add comprehensive error handling and structured logging</step>
      </implementation-details>
      <code-structure>
        <class name="SearchService">
          <constructor>
            <parameter name="openLibraryService" type="OpenLibraryService" description="OpenLibrary service from Phase 2 for online search" />
            <parameter name="logger" type="Logger" description="Scoped logger for search operations" />
          </constructor>
          <method name="searchLocal" return-type="Fuse.FuseResult<Book>[]">
            <parameter name="books" type="Book[]" description="Local books to search through" />
            <parameter name="term" type="string" description="Search term" />
            <description>Performs local fuzzy search using Fuse.js with optimized configuration</description>
          </method>
          <method name="searchOnline" return-type="Promise<BookSearchResult[]>">
            <parameter name="term" type="string" description="Search term" />
            <parameter name="limit" type="number" default="10" description="Maximum results" />
            <parameter name="localBooks" type="Book[]" default="[]" description="Local books for deduplication" />
            <description>Delegates to OpenLibraryService for online search</description>
          </method>
          <method name="searchHybrid" return-type="Promise<{localResults: Book[], onlineResults: BookSearchResult[]}>">
            <parameter name="searchTerm" type="string" description="Search term" />
            <parameter name="includeOnline" type="boolean" default="true" description="Whether to include online results" />
            <parameter name="onlineLimit" type="number" default="10" description="Maximum online results" />
            <description>Orchestrates local and online search with intelligent result merging</description>
          </method>
          <method name="mergeAndRankResults" return-type="Book[]">
            <parameter name="localResults" type="Fuse.FuseResult<Book>[]" description="Local search results with Fuse.js scores" />
            <parameter name="onlineResults" type="BookSearchResult[]" description="Online search results" />
            <description>Intelligently merges and ranks results from both sources</description>
          </method>
          <private-method name="configureFuse" return-type="Fuse<Book>">
            <parameter name="books" type="Book[]" description="Books to create Fuse index for" />
            <description>Creates optimally configured Fuse.js instance</description>
          </private-method>
          <private-method name="preprocessQuery" return-type="string[]">
            <parameter name="query" type="string" description="Raw search query" />
            <description>Preprocesses query for better matching (preserved from original)</description>
          </private-method>
        </class>
      </code-structure>
      <modernization-actions>
        <action type="deletion">COMPLETELY DELETE calculateEnhancedRelevanceScore function (lines 369-525) - 156 lines of custom logic</action>
        <action type="deletion">COMPLETELY DELETE getEnhancedStringSimilarity function (lines 244-358) - 114 lines of custom algorithm</action>
        <action type="deletion">COMPLETELY DELETE getEditDistance function (lines 189-236) - 47 lines of Levenshtein implementation</action>
        <action type="deletion">COMPLETELY DELETE normalizeString function (lines 237-243) - 6 lines of string normalization</action>
         <action type="fuse-integration">Replace all custom scoring with Fuse.js configuration: keys: ['title', 'author', 'isbn'], threshold: 0.4, includeScore: true</action>
        <action type="result-processing">Use Fuse.js scores instead of custom relevance calculations</action>
        <action type="service-integration">Integrate OpenLibraryService for online search delegation</action>
        <action type="logging">Replace console.log with structured logging throughout search operations</action>
        <action type="error-handling">Add comprehensive error handling for search failures</action>
        <action type="performance">Optimize Fuse.js configuration for book search use case</action>
      </modernization-actions>
      <implementation-guidance>
        <guidance type="fuse-configuration">
          <optimal-settings>
            <setting name="keys">['title', 'author', 'isbn', 'description'] - weighted by importance</setting>
            <setting name="threshold">0.4 - balance between precision and recall</setting>
            <setting name="includeScore">true - for result ranking</setting>
            <setting name="includeMatches">true - for highlighting matched terms</setting>
            <setting name="minMatchCharLength">2 - avoid single character matches</setting>
            <setting name="ignoreLocation">true - match anywhere in text</setting>
          </optimal-settings>
        </guidance>
        <guidance type="function-extraction">
          <source-location>books-api.ts lines 1310-1350 (searchBooksHybrid)</source-location>
          <target-method>searchHybrid</target-method>
          <key-considerations>
            <consideration>Preserve local database search integration</consideration>
            <consideration>Maintain online search delegation to OpenLibraryService</consideration>
            <consideration>Keep result logging and debugging information</consideration>
            <consideration>Handle online search failures gracefully</consideration>
          </key-considerations>
        </guidance>
        <guidance type="result-merging">
          <strategy>Use Fuse.js scores for local results and OpenLibrary relevance scores for online results</strategy>
          <deduplication>Compare ISBN, title, and author to identify duplicates between local and online results</deduplication>
          <ranking>Prioritize local results with high Fuse.js scores, then online results with high relevance scores</ranking>
        </guidance>
      </implementation-guidance>
      <success-criteria>
        <criterion>All custom search logic (300+ lines) is completely deleted</criterion>
        <criterion>Fuse.js provides accurate and configurable search results</criterion>
        <criterion>Search performance is maintained or improved</criterion>
        <criterion>Hybrid search combines local and online results effectively</criterion>
        <criterion>Service integrates properly with OpenLibraryService from Phase 2</criterion>
        <criterion>Structured logging is implemented throughout</criterion>
      </success-criteria>
      <risks>
        <risk severity="medium">Search accuracy changes affecting user experience</risk>
        <risk severity="low">Fuse.js configuration optimization needed</risk>
        <risk severity="low">Performance differences with large book collections</risk>
      </risks>
    </task>

    <task id="3.2" priority="critical" estimated-time="8 hours">
      <name>Extract Book Service</name>
      <description>Create central orchestrator service for core business logic, integrating all other services with comprehensive Zod validation</description>
      <files-to-create>
        <file>electron/main/api/books-api/services/book.service.ts</file>
      </files-to-create>
      <files-to-modify>
        <file>electron/main/api/books-api.ts</file>
      </files-to-modify>
      <codebase-analysis>
        <current-location>
          <function name="validateAndTransformBookData" lines="860-903" complexity="medium">
            <description>Manual validation function with imperative checks for book data</description>
            <dependencies>None - standalone validation logic</dependencies>
            <current-issues>Manual validation, no schema reuse, imperative error handling</current-issues>
          </function>
          <function name="createBookWithValidation" lines="1045-1137" complexity="high">
            <description>Book creation with cover download, validation, folder creation, and database integration</description>
            <dependencies>validateAndTransformBookData, downloadCoverImageData, createBook, ensureBooksRootFolder, createFolderWithValidation, notifyBookChange</dependencies>
            <current-issues>Complex orchestration, direct cover processing, manual validation</current-issues>
          </function>
          <function name="updateBookWithValidation" lines="1139-1161" complexity="low">
            <description>Book update with validation and database hooks</description>
            <dependencies>updateBook, notifyBookChange</dependencies>
            <current-issues>Minimal validation, no service delegation</current-issues>
          </function>
          <function name="deleteBookAndHandleFolder" lines="1163-1245" complexity="high">
            <description>Complex book deletion with folder management, note handling, and cleanup</description>
            <dependencies>getBookFolder, getNotesByFolderId, updateNote, updateFolderWithValidation, deleteFolderFromDb, deleteBookFromDb, notifyBookChange</dependencies>
            <current-issues>Complex orchestration logic, multiple database operations, extensive error handling</current-issues>
          </function>
        </current-location>
      </codebase-analysis>
      <implementation-details>
        <step>Analyze core CRUD logic in books-api.ts (lines 860-1245)</step>
        <step>Create BookService class with comprehensive dependency injection</step>
        <step>Extract createBookWithValidation function (lines 1045-1137) with service integration</step>
        <step>Extract updateBookWithValidation function (lines 1139-1161) with enhanced validation</step>
        <step>Extract deleteBookAndHandleFolder function (lines 1163-1245) with complex orchestration</step>
        <step>REPLACE validateAndTransformBookData with Zod schema validation</step>
        <step>Delegate cover processing to CoverService from Phase 2</step>
         <step>Enforce 10MB max input size for images at transport and validation layers</step>
         <step>Preserve original image format by default (no forced WebP conversion)</step>
         <step>Save covers using media-api with filename 'cover.&lt;ext&gt;' derived from detected format and correct MIME type</step>
        <step>Integrate with database-api for all CRUD operations</step>
        <step>Integrate with folders-api for folder management</step>
        <step>Add comprehensive error handling and structured logging</step>
        <step>Preserve database hooks and notification system</step>
      </implementation-details>
      <code-structure>
        <class name="BookService">
          <constructor>
            <parameter name="coverService" type="CoverService" description="Cover processing service from Phase 2" />
            <parameter name="searchService" type="SearchService" description="Search service from this phase" />
            <parameter name="databaseApi" type="DatabaseApi" description="Database operations interface" />
            <parameter name="foldersApi" type="FoldersApi" description="Folder management interface" />
            <parameter name="logger" type="Logger" description="Scoped logger for book operations" />
          </constructor>
          <method name="createBook" return-type="Promise<Book>">
            <parameter name="bookData" type="CreateBookPayload" description="Book data validated with Zod" />
            <parameter name="downloadCover" type="boolean" default="true" description="Whether to download cover image" />
            <description>Creates book with validation, cover processing, and folder setup</description>
          </method>
          <method name="updateBook" return-type="Promise<Book>">
            <parameter name="id" type="number" description="Book ID to update" />
            <parameter name="updates" type="UpdateBookPayload" description="Updates validated with Zod" />
            <description>Updates book with validation and database hooks</description>
          </method>
          <method name="deleteBook" return-type="Promise<{success: boolean, id: number}>">
            <parameter name="id" type="number" description="Book ID to delete" />
            <description>Deletes book with folder cleanup and note handling</description>
          </method>
          <method name="getBookById" return-type="Promise<Book | null>">
            <parameter name="id" type="number" description="Book ID to retrieve" />
            <description>Retrieves single book by ID</description>
          </method>
          <method name="getAllBooks" return-type="Promise<Book[]>">
            <description>Retrieves all books</description>
          </method>
          <method name="getBooksWithMetadata" return-type="Promise<BookWithNoteCount[]>">
            <description>Retrieves books with note counts and metadata</description>
          </method>
          <private-method name="validateBookData" return-type="Book">
            <parameter name="data" type="Partial<Book>" description="Raw book data" />
            <description>Validates book data using Zod schemas</description>
          </private-method>
          <private-method name="handleCoverProcessing" return-type="Promise<string | null>">
            <parameter name="coverUrl" type="string" description="Cover URL to process" />
            <description>Delegates cover processing to CoverService</description>
          </private-method>
        </class>
      </code-structure>
      <modernization-actions>
        <action type="validation-replacement">REPLACE validateAndTransformBookData with CreateBookSchema.parse() and UpdateBookSchema.parse()</action>
        <action type="service-delegation">Delegate all cover processing to this.coverService.processCoverUrl()</action>
        <action type="zod-validation">Add comprehensive Zod validation at all entry points</action>
        <action type="error-handling">Implement structured error handling with meaningful messages</action>
        <action type="logging">Replace console.log with this.logger for all operations</action>
        <action type="orchestration">Maintain complex orchestration logic but with service delegation</action>
        <action type="database-integration">Preserve all database-api and folders-api integrations</action>
        <action type="hooks-preservation">Maintain notifyBookChange and database hooks</action>
      </modernization-actions>
      <implementation-guidance>
        <guidance type="function-extraction">
          <source-location>books-api.ts lines 1045-1137 (createBookWithValidation)</source-location>
          <target-method>createBook</target-method>
          <key-considerations>
            <consideration>Preserve cover download logic but delegate to CoverService</consideration>
            <consideration>Maintain folder creation and book folder setup</consideration>
            <consideration>Keep database hooks and notification system</consideration>
            <consideration>Replace manual validation with Zod schema validation</consideration>
          </key-considerations>
        </guidance>
        <guidance type="function-extraction">
          <source-location>books-api.ts lines 1163-1245 (deleteBookAndHandleFolder)</source-location>
          <target-method>deleteBook</target-method>
          <key-considerations>
            <consideration>Preserve complex folder and note handling logic</consideration>
            <consideration>Maintain database transaction-like behavior</consideration>
            <consideration>Keep extensive error handling and logging</consideration>
            <consideration>Preserve folder cleanup and note unlinking logic</consideration>
          </key-considerations>
        </guidance>
        <guidance type="validation-modernization">
          <old-pattern>validateAndTransformBookData with manual checks</old-pattern>
          <new-pattern>CreateBookSchema.parse() with automatic validation and type inference</new-pattern>
          <benefits>Type safety, consistent validation, single source of truth</benefits>
        </guidance>
      </implementation-guidance>
      <success-criteria>
        <criterion>All input data is validated with Zod schemas</criterion>
        <criterion>Service properly orchestrates all dependencies</criterion>
        <criterion>No direct external API calls or cover processing in business logic</criterion>
        <criterion>Comprehensive error handling is implemented</criterion>
        <criterion>Covers are stored as WebP with correct content type and naming</criterion>
        <criterion>All existing functionality is preserved</criterion>
        <criterion>Database hooks and notifications continue to work</criterion>
      </success-criteria>
      <risks>
        <risk severity="medium">Complex orchestration logic may introduce bugs</risk>
        <risk severity="low">Database integration issues</risk>
        <risk severity="low">Folder management complexity</risk>
      </risks>
    </task>

    <task id="3.3" priority="high" estimated-time="4 hours">
      <name>Legacy Code Cleanup</name>
      <description>Remove or comment extracted functions from original books-api.ts, significantly reducing file size and complexity</description>
      <files-to-modify>
        <file>electron/main/api/books-api.ts</file>
      </files-to-modify>
      <implementation-details>
        <step>Comment out extracted OpenLibrary functions (searchBooksOnline, getBookDetailsFromOpenLibrary)</step>
        <step>Comment out extracted cover processing functions (downloadCoverImageData)</step>
        <step>COMPLETELY DELETE custom search and scoring functions (300+ lines)</step>
        <step>Comment out extracted CRUD functions (createBookWithValidation, updateBookWithValidation, deleteBookAndHandleFolder)</step>
        <step>Comment out validation helper function (validateAndTransformBookData)</step>
        <step>Add TODO comments pointing to Phase 4 integration</step>
        <step>Ensure remaining code still compiles without errors</step>
        <step>Update export statements to reflect commented functions</step>
      </implementation-details>
      <functions-to-delete>
        <function name="calculateEnhancedRelevanceScore" lines="369-525" description="156 lines of custom relevance scoring"/>
        <function name="getEnhancedStringSimilarity" lines="244-358" description="114 lines of custom string similarity"/>
        <function name="getEditDistance" lines="189-236" description="47 lines of Levenshtein distance calculation"/>
        <function name="normalizeString" lines="237-243" description="6 lines of string normalization"/>
      </functions-to-delete>
      <functions-to-comment>
        <function name="searchBooksOnline" lines="568-678" description="Online search - moved to OpenLibraryService"/>
        <function name="getBookDetailsFromOpenLibrary" lines="681-857" description="Book details - moved to OpenLibraryService"/>
        <function name="downloadCoverImageData" lines="122-188" description="Cover download - moved to CoverService"/>
        <function name="createBookWithValidation" lines="1045-1137" description="Book creation - moved to BookService"/>
        <function name="updateBookWithValidation" lines="1139-1161" description="Book update - moved to BookService"/>
        <function name="deleteBookAndHandleFolder" lines="1163-1245" description="Book deletion - moved to BookService"/>
        <function name="validateAndTransformBookData" lines="860-903" description="Validation - replaced with Zod"/>
        <function name="searchBooksHybrid" lines="1310-1350" description="Hybrid search - moved to SearchService"/>
      </functions-to-comment>
      <success-criteria>
        <criterion>Custom search functions (300+ lines) are completely deleted</criterion>
        <criterion>Extracted functions are commented out with clear TODO comments</criterion>
        <criterion>File still compiles without TypeScript errors</criterion>
        <criterion>Export statements are updated to reflect changes</criterion>
        <criterion>File size is significantly reduced (target: 50% reduction)</criterion>
        <criterion>Clear documentation for Phase 4 integration</criterion>
      </success-criteria>
      <risks>
        <risk severity="low">Accidental deletion of needed code</risk>
        <risk severity="low">Export statement mismatches</risk>
      </risks>
    </task>
  </tasks>
  
   <!-- New Maintenance Service to preserve runtime utilities and parity -->
  <tasks>
    <task id="3.4" priority="high" estimated-time="8 hours">
      <name>Create Maintenance Service</name>
      <description>Implement utilities previously hosted in the monolithic file to ensure data integrity and operational parity</description>
      <files-to-create>
        <file>electron/main/api/books-api/services/maintenance.service.ts</file>
      </files-to-create>
      <files-to-modify>
        <file>electron/main/ipc-handlers.ts</file>
        <file>electron/main/api/books-api/index.ts</file>
      </files-to-modify>
      <implementation-details>
        <step>Implement checkAndDownloadMissingCovers: query books with non-empty cover_url and no cover in media_files; download via CoverService (supports http(s), data:, noti-media://) and save with media-api</step>
        <step>Implement ensureFoldersForAllBooks: ensure one-to-one book↔folder mapping using folders-api; respect unique index on folders(book_id)</step>
        <step>Implement getBooksWithoutFolders: SELECT books without a corresponding folder</step>
        <step>Implement cleanupBase64CoverUrls: for books.cover_url starting with data:, write file via media-api and clear DB field to reduce DB footprint</step>
        <step>Expose these methods through books-api facade to preserve current IPC endpoints</step>
        <step>Enforce 10MB max image size at download time and during validation; preserve original format unless explicitly configured otherwise</step>
      </implementation-details>
      <success-criteria>
        <criterion>All four utilities are implemented and callable via facade</criterion>
        <criterion>Startup background job checkAndDownloadMissingCovers continues to work</criterion>
        <criterion>No duplicate book folders are created; unique constraint is respected</criterion>
        <criterion>Base64 cover cleanup reduces DB footprint and sync manifest size</criterion>
      </success-criteria>
      <risks>
        <risk severity="medium">Potential rate limiting when downloading many covers</risk>
        <risk severity="low">Unique index conflicts when ensuring folders</risk>
      </risks>
    </task>
  </tasks>

  <validation-steps>
    <step id="1" name="Service Compilation">
      <description>Verify all new services compile without TypeScript errors</description>
      <command>npx tsc --noEmit</command>
      <expected-result>No TypeScript compilation errors for SearchService and BookService</expected-result>
    </step>
    <step id="2" name="Search Service Integration">
      <description>Test SearchService with Fuse.js and OpenLibraryService integration</description>
      <command>Create test script: const service = new SearchService(openLibraryService, logger); await service.searchHybrid('test');</command>
      <expected-result>Hybrid search returns results using Fuse.js for local and OpenLibrary for online</expected-result>
    </step>
    <step id="3" name="Book Service Orchestration">
      <description>Test BookService orchestration with all dependencies</description>
      <command>Create test script: const service = new BookService(coverService, searchService, databaseApi, foldersApi, logger); await service.createBook(testData);</command>
      <expected-result>Book creation works with proper service delegation and Zod validation</expected-result>
    </step>
    <step id="4" name="Legacy Code Cleanup Verification">
      <description>Verify books-api.ts compiles after cleanup and has significantly reduced size</description>
      <command>Check file size and compilation: wc -l books-api.ts && npx tsc --noEmit</command>
      <expected-result>File size reduced by ~50% and still compiles without errors</expected-result>
    </step>
    <step id="5" name="Fuse.js Performance">
      <description>Verify Fuse.js search performance with large book collections</description>
      <command>Test search with 1000+ books and measure response time</command>
      <expected-result>Search completes in <100ms for typical collections</expected-result>
    </step>
  </validation-steps>

  <risk-assessment>
    <overall-risk>Medium</overall-risk>
    <risk-factors>
      <factor name="Search Accuracy Changes" probability="medium" impact="high">
        <mitigation>Extensive testing with real book data and user feedback collection</mitigation>
      </factor>
      <factor name="Complex Service Orchestration" probability="medium" impact="medium">
        <mitigation>Comprehensive unit tests and integration tests for BookService</mitigation>
      </factor>
      <factor name="Large Code Deletion" probability="low" impact="high">
        <mitigation>Careful review of deleted code and comprehensive testing before deletion</mitigation>
      </factor>
      <factor name="Fuse.js Configuration" probability="medium" impact="low">
        <mitigation>Iterative tuning of Fuse.js parameters based on search result quality</mitigation>
      </factor>
    </risk-factors>
  </risk-assessment>

   <success-criteria>
     <criterion>SearchService replaces 300+ lines of custom search logic with Fuse.js</criterion>
     <criterion>BookService orchestrates all services with comprehensive Zod validation</criterion>
     <criterion>Covers are saved locally in original format with correct MIME and extension</criterion>
     <criterion>All services use dependency injection and can be instantiated independently</criterion>
     <criterion>Legacy books-api.ts file is significantly reduced in size and complexity</criterion>
     <criterion>All existing functionality is preserved while modernizing implementation</criterion>
     <criterion>Search performance is maintained or improved with Fuse.js</criterion>
     <criterion>Foundation is established for Phase 4 integration and testing</criterion>
   </success-criteria>

  <cross-references>
    <reference phase="phase-2" task="2.1">
      <description>Uses OpenLibraryService created in Phase 2</description>
    </reference>
    <reference phase="phase-2" task="2.2">
      <description>Uses CoverService created in Phase 2</description>
    </reference>
    <reference phase="phase-1" task="1.5">
      <description>Uses Zod schemas for validation throughout BookService</description>
    </reference>
    <reference phase="phase-4" task="4.1">
      <description>Services will be integrated into public API in Phase 4</description>
    </reference>
    <reference phase="phase-4" task="4.2">
      <description>All services will be unit tested in Phase 4</description>
    </reference>
  </cross-references>
</refactoring-phase>
