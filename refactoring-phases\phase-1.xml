<?xml version="1.0" encoding="UTF-8"?>
<refactoring-phase id="1" name="Foundation & Infrastructure">
  <metadata>
    <estimated-duration>2 days</estimated-duration>
    <risk-level>low</risk-level>
    <dependencies>
      <dependency type="none">No dependencies - this is the foundation phase</dependency>
    </dependencies>
    <next-phase>phase-2</next-phase>
  </metadata>

  <objectives>
    <primary-goal>Set up the new module structure and introduce core utility libraries without modifying existing application logic</primary-goal>
    <secondary-goals>
      <goal>Create isolated namespace for new Books API module</goal>
      <goal>Install and verify all new dependencies</goal>
      <goal>Establish centralized logging and HTTP client infrastructure</goal>
      <goal>Define single source of truth for data validation with Zod schemas</goal>
    </secondary-goals>
  </objectives>

  <milestones>
    <milestone id="1.1" name="Directory Structure Created">
      <description>New books-api directory structure is established</description>
      <validation>Verify all directories exist in correct hierarchy</validation>
    </milestone>
    <milestone id="1.2" name="Dependencies Installed">
      <description>All new libraries are installed and verified</description>
      <validation>Check package.json and run npm list to verify installations</validation>
    </milestone>
    <milestone id="1.3" name="Utilities Implemented">
      <description>Centralized logger and HTTP client are ready for use</description>
      <validation>Test logger output and HTTP client configuration</validation>
    </milestone>
    <milestone id="1.4" name="Schemas Defined">
      <description>Zod schemas and TypeScript types are fully defined</description>
      <validation>Compile TypeScript and verify type inference works</validation>
    </milestone>
  </milestones>

  <tasks>
    <task id="1.1" priority="critical" estimated-time="30 minutes">
      <name>Create Directory Structure</name>
      <description>Create the new books-api module directory hierarchy</description>
      <files-to-create>
        <directory>electron/main/api/books-api/</directory>
        <directory>electron/main/api/books-api/services/</directory>
        <directory>electron/main/api/books-api/utils/</directory>
        <directory>electron/main/api/books-api/__tests__/</directory>
        <directory>electron/main/api/books-api/__tests__/integration/</directory>
      </files-to-create>
      <implementation-details>
        <step>Navigate to electron/main/api/ directory</step>
        <step>Create books-api folder</step>
        <step>Create subdirectories: services, utils, __tests__, __tests__/integration</step>
        <step>Verify directory structure matches target architecture</step>
      </implementation-details>
      <success-criteria>
        <criterion>All directories exist in correct hierarchy</criterion>
        <criterion>Directory structure matches architectural plan</criterion>
      </success-criteria>
      <risks>
        <risk severity="low">Directory creation permissions</risk>
      </risks>
    </task>

    <task id="1.2" priority="critical" estimated-time="15 minutes">
      <name>Install New Dependencies</name>
      <description>Install all required libraries for the modernized stack</description>
      <dependencies-to-install>
        <dependency name="fuse.js" purpose="Fuzzy search functionality" />
        <dependency name="sharp" purpose="Image processing" />
        <dependency name="zod" purpose="Schema validation and type inference" />
        <dependency name="electron-log" purpose="Centralized logging" />
        <dependency name="date-fns" purpose="Date manipulation utilities" />
      </dependencies-to-install>
      <implementation-details>
        <step>Open terminal in project root directory</step>
        <step>Execute: npm install fuse.js sharp zod electron-log date-fns</step>
        <step>Verify package.json is updated with new dependencies</step>
        <step>Run npm list to confirm successful installation</step>
        <step>Check for any version conflicts or warnings</step>
      </implementation-details>
      <success-criteria>
        <criterion>All dependencies appear in package.json</criterion>
        <criterion>No installation errors or conflicts</criterion>
        <criterion>Dependencies are available for import</criterion>
      </success-criteria>
      <risks>
        <risk severity="medium">Version conflicts with existing dependencies</risk>
        <risk severity="low">Network issues during installation</risk>
      </risks>
    </task>

    <task id="1.3" priority="high" estimated-time="45 minutes">
      <name>Implement Centralized Logger</name>
      <description>Create electron-log configuration for the books-api module</description>
      <files-to-create>
        <file>electron/main/api/books-api/utils/logger.ts</file>
      </files-to-create>
      <implementation-details>
        <step>Create logger.ts file in utils directory</step>
        <step>Import electron-log library</step>
        <step>Configure file transport with info level</step>
        <step>Configure console transport with debug level</step>
        <step>Set custom format for both transports</step>
        <step>Create scoped logger instance for 'books-api'</step>
        <step>Export configured logger as default</step>
      </implementation-details>
      <code-template>
        <content>
import log from 'electron-log';

// Configure the logger
log.transports.file.setLevel('info');
log.transports.console.setLevel('debug');
log.transports.file.setFormat('{y}-{m}-{d} {h}:{i}:{s}.{ms} [{level}] {text}');
log.transports.console.setFormat('{h}:{i}:{s}.{ms} [{level}] {text}');

// Add a category for easy filtering
const logger = log.scope('books-api');

export default logger;
        </content>
      </code-template>
      <success-criteria>
        <criterion>Logger file compiles without errors</criterion>
        <criterion>Logger can be imported and used</criterion>
        <criterion>Log output includes books-api scope</criterion>
      </success-criteria>
      <risks>
        <risk severity="low">Electron-log configuration issues</risk>
      </risks>
    </task>

    <task id="1.4" priority="high" estimated-time="60 minutes">
      <name>Implement Centralized HTTP Client</name>
      <description>Create axios instance with logging interceptors</description>
      <files-to-create>
        <file>electron/main/api/books-api/utils/http.client.ts</file>
      </files-to-create>
      <implementation-details>
        <step>Create http.client.ts file in utils directory</step>
        <step>Import axios and logger</step>
        <step>Create axios instance with timeout and User-Agent</step>
        <step>Add request interceptor for logging outgoing requests</step>
        <step>Add response interceptor for logging responses and errors</step>
        <step>Export configured axios instance as default</step>
      </implementation-details>
      <code-template>
        <content>
import axios from 'axios';
import logger from './logger';

const httpClient = axios.create({
  timeout: 25000, // Default timeout
  headers: {
    'User-Agent': 'Noti/2.0 (https://github.com/your-repo/noti)',
  },
});

// Request Interceptor for Logging
httpClient.interceptors.request.use(request => {
  logger.debug(`--> ${request.method?.toUpperCase()} ${request.url}`);
  return request;
}, error => {
  logger.error(`REQUEST ERROR: ${error.message}`);
  return Promise.reject(error);
});

// Response Interceptor for Logging
httpClient.interceptors.response.use(response => {
  logger.debug(`<-- ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`);
  return response;
}, error => {
  if (axios.isAxiosError(error) && error.response) {
    logger.error(`<-- ${error.response.status} ${error.config.method?.toUpperCase()} ${error.config.url}`);
  } else {
    logger.error(`RESPONSE ERROR: ${error.message}`);
  }
  return Promise.reject(error);
});

export default httpClient;
        </content>
      </code-template>
      <success-criteria>
        <criterion>HTTP client file compiles without errors</criterion>
        <criterion>Interceptors are properly configured</criterion>
        <criterion>Logger integration works correctly</criterion>
      </success-criteria>
      <risks>
        <risk severity="low">Axios configuration issues</risk>
        <risk severity="low">Logger integration problems</risk>
      </risks>
    </task>
  </tasks>

  <validation-steps>
    <step id="1" name="Directory Structure Verification">
      <description>Verify all directories are created correctly</description>
      <command>ls -la electron/main/api/books-api/</command>
      <expected-result>Should show services, utils, __tests__ directories</expected-result>
    </step>
    <step id="2" name="Dependencies Verification">
      <description>Verify all dependencies are installed</description>
      <command>npm list fuse.js sharp zod electron-log date-fns</command>
      <expected-result>Should show all dependencies with version numbers</expected-result>
    </step>
    <step id="3" name="Logger Test">
      <description>Test logger functionality</description>
      <command>Create test script to import and use logger</command>
      <expected-result>Should output formatted log messages with books-api scope</expected-result>
    </step>
    <step id="4" name="HTTP Client Test">
      <description>Test HTTP client configuration</description>
      <command>Create test script to import HTTP client</command>
      <expected-result>Should create axios instance with interceptors</expected-result>
    </step>
  </validation-steps>

  <risk-assessment>
    <overall-risk>Low</overall-risk>
    <risk-factors>
      <factor name="Dependency Conflicts" probability="medium" impact="low">
        <mitigation>Check for conflicts before installation, use specific versions if needed</mitigation>
      </factor>
      <factor name="Directory Permissions" probability="low" impact="low">
        <mitigation>Ensure proper file system permissions</mitigation>
      </factor>
      <factor name="Configuration Issues" probability="low" impact="medium">
        <mitigation>Test each utility after implementation</mitigation>
      </factor>
    </risk-factors>
  </risk-assessment>

  <success-criteria>
    <criterion>All directory structure is in place</criterion>
    <criterion>All dependencies are successfully installed</criterion>
    <criterion>Logger outputs correctly formatted messages</criterion>
    <criterion>HTTP client is configured with interceptors</criterion>
    <criterion>Zod schemas compile and generate correct TypeScript types</criterion>
    <criterion>No existing application functionality is affected</criterion>
  </success-criteria>

  <additional-tasks>
    <task id="1.5" priority="critical" estimated-time="90 minutes">
      <name>Define Zod Schemas</name>
      <description>Create comprehensive Zod schemas for all book-related data structures</description>
      <files-to-create>
        <file>electron/main/api/books-api/book.schemas.ts</file>
      </files-to-create>
      <implementation-details>
        <step>Create book.schemas.ts file</step>
        <step>Import Zod library</step>
        <step>Define BookSchema with all required and optional fields</step>
        <step>Define OpenLibraryBookSchema for external API data</step>
        <step>Define CreateBookSchema (omitting generated fields)</step>
        <step>Define UpdateBookSchema (all fields optional)</step>
        <step>Add validation messages and constraints</step>
        <step>Export all schemas</step>
      </implementation-details>
      <code-template>
        <content>
import { z } from 'zod';

export const BookSchema = z.object({
  id: z.number().optional(),
  title: z.string().min(1, { message: "Title is required" }),
  author: z.string().nullable().optional(),
  isbn: z.string().nullable().optional(),
  cover_url: z.string().url().nullable().optional(),
  publication_date: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  page_count: z.number().int().positive().nullable().optional(),
  current_page: z.number().int().positive().nullable().optional(),
  rating: z.number().min(0).max(5).nullable().optional(),
  language: z.string().nullable().optional(),
  genres: z.string().nullable().optional(),
  olid: z.string().nullable().optional(),
  status: z.enum(['unread', 'reading', 'completed', 'to-read']).default('unread'),
  custom_fields: z.string().nullable().optional(),
  created_at: z.string().datetime().optional(),
  updated_at: z.string().datetime().optional(),
});

export const OpenLibraryBookSchema = z.object({
  title: z.string(),
  author_name: z.array(z.string()).optional(),
  isbn: z.array(z.string()).optional(),
  cover_i: z.number().optional(),
  key: z.string(),
  first_publish_year: z.number().optional(),
  publisher: z.array(z.string()).optional(),
  language: z.array(z.string()).optional(),
  subject: z.array(z.string()).optional(),
});

// Schema for creating a book (omits generated fields)
export const CreateBookSchema = BookSchema.omit({ id: true, created_at: true, updated_at: true });

// Schema for updating a book (all fields are optional)
export const UpdateBookSchema = BookSchema.partial();

// Schema for search results
export const SearchResultSchema = z.object({
  books: z.array(BookSchema),
  total: z.number(),
  query: z.string(),
  source: z.enum(['local', 'online', 'hybrid']),
});
        </content>
      </code-template>
      <success-criteria>
        <criterion>All schemas compile without TypeScript errors</criterion>
        <criterion>Schemas cover all existing book data fields</criterion>
        <criterion>Validation rules are appropriate for each field</criterion>
      </success-criteria>
    </task>

    <task id="1.6" priority="critical" estimated-time="30 minutes">
      <name>Generate TypeScript Types</name>
      <description>Create TypeScript types inferred from Zod schemas</description>
      <files-to-create>
        <file>electron/main/api/books-api/book.types.ts</file>
      </files-to-create>
      <implementation-details>
        <step>Create book.types.ts file</step>
        <step>Import Zod and all schemas</step>
        <step>Use z.infer to generate TypeScript types</step>
        <step>Export all generated types</step>
        <step>Verify types are correctly inferred</step>
      </implementation-details>
      <code-template>
        <content>
import { z } from 'zod';
import {
  BookSchema,
  CreateBookSchema,
  UpdateBookSchema,
  OpenLibraryBookSchema,
  SearchResultSchema
} from './book.schemas';

export type Book = z.infer<typeof BookSchema>;
export type CreateBookPayload = z.infer<typeof CreateBookSchema>;
export type UpdateBookPayload = z.infer<typeof UpdateBookSchema>;
export type OpenLibraryBook = z.infer<typeof OpenLibraryBookSchema>;
export type SearchResult = z.infer<typeof SearchResultSchema>;

// Additional utility types
export type BookStatus = Book['status'];
export type BookId = NonNullable<Book['id']>;
export type BookWithId = Book & { id: BookId };
        </content>
      </code-template>
      <success-criteria>
        <criterion>All types are correctly inferred from schemas</criterion>
        <criterion>Types compile without errors</criterion>
        <criterion>Types can be imported and used</criterion>
      </success-criteria>
    </task>
  </additional-tasks>
</refactoring-phase>
