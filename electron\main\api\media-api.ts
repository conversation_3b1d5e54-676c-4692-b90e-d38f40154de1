// Media API operations
import path from 'node:path';
import fs from 'node:fs';
import { app } from 'electron';
import { dbRun, dbGet, dbAll } from '../database/database-api';

// Define interfaces for media files
export interface MediaFile {
  id?: number;
  note_id: number | null;
  book_id: number | null;
  file_path: string;
  file_name: string;
  file_type: string;
  file_size: number;
  is_cover: boolean;
  created_at?: string;
}

// Helper to get media storage path
export const getMediaStoragePath = (): string => {
  const userDataPath = app.getPath('userData');
  const mediaPath = path.join(userDataPath, 'media');
  
  // Ensure media directory exists (sync is acceptable at app start path calculation)
  if (!fs.existsSync(mediaPath)) {
    fs.mkdirSync(mediaPath, { recursive: true });
  }
  
  return mediaPath;
};

// Helper to convert a file path to a noti-media URL
export const filePathToMediaUrl = (filePath: string): string => {
  try {
    // Normalize the path first to handle any inconsistencies
    const normalizedPath = path.normalize(filePath);
    
    // For Windows, ensure we have proper path separators
    let urlPath = normalizedPath;
    
    // Convert Windows backslashes to forward slashes for URL
    if (process.platform === 'win32') {
      urlPath = normalizedPath.replace(/\\/g, '/');
    }
    
    // Encode the path components to handle special characters
    const pathParts = urlPath.split('/');
    const encodedParts = pathParts.map(part => {
      // Don't encode the drive letter colon on Windows (e.g., C:)
      if (process.platform === 'win32' && part.match(/^[A-Za-z]:$/)) {
        return part;
      }
      return encodeURIComponent(part);
    });
    
    const encodedPath = encodedParts.join('/');
    
    return `noti-media://${encodedPath}`;
  } catch (error) {
    console.error('Error converting file path to media URL:', error);
    // Fallback to simple conversion
    const fallbackPath = filePath.replace(/\\/g, '/');
    return `noti-media://${fallbackPath}`;
  }
};

// Create a new media file entry and save the file to disk
export const saveMediaFile = async (
  noteId: number | null,
  fileData: number[] | Buffer,
  fileName: string,
  fileType: string,
  bookId: number | null = null,
  isCover: boolean = false
): Promise<MediaFile> => {
  try {
    // Create unique filename to prevent collisions
    const timestamp = Date.now();
    const uniqueFileName = `${timestamp}-${fileName}`;
    const mediaStoragePath = getMediaStoragePath();
    const filePath = path.join(mediaStoragePath, uniqueFileName);

    // Ensure parent directory exists asynchronously
    await fs.promises.mkdir(path.dirname(filePath), { recursive: true });
    
    // Convert array to Buffer if needed
    const fileBuffer = Buffer.isBuffer(fileData) ? fileData : Buffer.from(fileData);
    
    // Save file to disk asynchronously
    await fs.promises.writeFile(filePath, fileBuffer);
    
    // Get file size
    const fileSize = fileBuffer.length;
    
    // Save entry to database
    const query = `
      INSERT INTO media_files (
        note_id, book_id, file_path, file_name, file_type, file_size, is_cover, created_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = dbRun(query, [
      noteId, bookId, filePath, uniqueFileName, fileType, fileSize, isCover ? 1 : 0, new Date().toISOString()
    ]);

    if (!result.lastInsertRowid) {
      throw new Error('Failed to create media file entry, no ID returned');
    }

    // Return the created media file
    return getMediaFileById(result.lastInsertRowid);
  } catch (error) {
    console.error('Error saving media file:', error);
    throw error;
  }
};

// Get a media file by ID
export const getMediaFileById = (id: number): MediaFile => {
  const query = 'SELECT * FROM media_files WHERE id = ?';

  try {
    const mediaFile = dbGet<MediaFile>(query, [id]);
    if (!mediaFile) {
      throw new Error(`Media file with ID ${id} not found`);
    }
    return mediaFile;
  } catch (error) {
    console.error(`Error getting media file with ID ${id}:`, error);
    throw error;
  }
};

// Get all media files for a note
export const getMediaFilesByNoteId = (noteId: number): MediaFile[] => {
  const query = 'SELECT * FROM media_files WHERE note_id = ? ORDER BY created_at DESC';

  try {
    return dbAll<MediaFile>(query, [noteId]);
  } catch (error) {
    console.error(`Error getting media files for note ID ${noteId}:`, error);
    throw error;
  }
};

// Get all media files for a book
export const getMediaFilesByBookId = (bookId: number): MediaFile[] => {
  const query = 'SELECT * FROM media_files WHERE book_id = ? ORDER BY created_at DESC';

  try {
    return dbAll<MediaFile>(query, [bookId]);
  } catch (error) {
    console.error(`Error getting media files for book ID ${bookId}:`, error);
    throw error;
  }
};

// Get book cover (first cover image for a book)
export const getBookCover = (bookId: number): MediaFile | null => {
  const query = 'SELECT * FROM media_files WHERE book_id = ? AND is_cover = 1 ORDER BY created_at DESC LIMIT 1';

  try {
    const result = dbGet<MediaFile>(query, [bookId]);
    return result || null;
  } catch (error) {
    console.error(`Error getting book cover for book ID ${bookId}:`, error);
    throw error;
  }
};

// Save book cover specifically
export const saveBookCover = async (
  bookId: number,
  coverData: Buffer,
  fileName: string = 'cover.jpg'
): Promise<MediaFile> => {
  // First, delete any existing cover for this book
  const existingCover = getBookCover(bookId);
  if (existingCover && existingCover.id) {
    await deleteMediaFile(existingCover.id);
  }

  // Detect image type from buffer to ensure correct extension and MIME
  const detectImageType = (buffer: Buffer): { ext: string; mime: string } | null => {
    if (!buffer || buffer.length < 12) return null;
    const b = buffer;
    // JPEG
    if (b[0] === 0xFF && b[1] === 0xD8 && b[2] === 0xFF) return { ext: 'jpg', mime: 'image/jpeg' };
    // PNG
    if (b[0] === 0x89 && b[1] === 0x50 && b[2] === 0x4E && b[3] === 0x47) return { ext: 'png', mime: 'image/png' };
    // WebP: RIFF....WEBP
    if (
      b[0] === 0x52 && b[1] === 0x49 && b[2] === 0x46 && b[3] === 0x46 &&
      b[8] === 0x57 && b[9] === 0x45 && b[10] === 0x42 && b[11] === 0x50
    ) return { ext: 'webp', mime: 'image/webp' };
    // GIF
    if (b[0] === 0x47 && b[1] === 0x49 && b[2] === 0x46) return { ext: 'gif', mime: 'image/gif' };
    // BMP
    if (b[0] === 0x42 && b[1] === 0x4D) return { ext: 'bmp', mime: 'image/bmp' };
    // TIFF LE/BE
    if (b[0] === 0x49 && b[1] === 0x49 && b[2] === 0x2A && b[3] === 0x00) return { ext: 'tiff', mime: 'image/tiff' };
    if (b[0] === 0x4D && b[1] === 0x4D && b[2] === 0x00 && b[3] === 0x2A) return { ext: 'tiff', mime: 'image/tiff' };
    return null;
  };

  const detected = detectImageType(coverData);

  // Normalize fileName extension to detected type if mismatch or default
  const lower = fileName.toLowerCase();
  let finalFileName = fileName;
  if (detected) {
    const expectedExt = `.${detected.ext}`;
    if (!(lower.endsWith(expectedExt))) {
      finalFileName = `cover.${detected.ext}`;
    }
  }

  // Determine content type from detection first, then from extension
  let contentType = detected?.mime || 'application/octet-stream';
  if (!detected) {
    if (lower.endsWith('.webp')) contentType = 'image/webp';
    else if (lower.endsWith('.jpg') || lower.endsWith('.jpeg')) contentType = 'image/jpeg';
    else if (lower.endsWith('.png')) contentType = 'image/png';
    else if (lower.endsWith('.gif')) contentType = 'image/gif';
    else if (lower.endsWith('.bmp')) contentType = 'image/bmp';
    else if (lower.endsWith('.tif') || lower.endsWith('.tiff')) contentType = 'image/tiff';
  }

  // Save the new cover with a stable per-book path and filename
  const mediaRoot = getMediaStoragePath();
  const bookDir = path.join(mediaRoot, `book-${bookId}`);
  await fs.promises.mkdir(bookDir, { recursive: true });

  const stableFileName = detected ? `cover.${detected.ext}` : finalFileName;
  const filePath = path.join(bookDir, stableFileName);

  await fs.promises.writeFile(filePath, coverData);

  const fileSize = coverData.length;

  const query = `
      INSERT INTO media_files (
        note_id, book_id, file_path, file_name, file_type, file_size, is_cover, created_at
      )
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

  const result = dbRun(query, [
    null, bookId, filePath, stableFileName, contentType, fileSize, 1, new Date().toISOString()
  ]);

  if (!result.lastInsertRowid) {
    throw new Error('Failed to create media file entry for book cover, no ID returned');
  }

  return getMediaFileById(result.lastInsertRowid);
};

// Delete a media file (from database and disk)
export const deleteMediaFile = async (id: number): Promise<{ success: boolean; id: number }> => {
  try {
    // First get the file path - will throw if not found
    const mediaFile = getMediaFileById(id);
    
    // Delete from database
    const query = 'DELETE FROM media_files WHERE id = ?';
    const result = dbRun(query, [id]);
    
    // If successful in database, delete from disk too
    if (result.changes && result.changes > 0) {
      try {
        await fs.promises.unlink(mediaFile.file_path);
      } catch (fsError: any) {
        if (fsError && fsError.code !== 'ENOENT') {
          console.error(`Error deleting file from disk: ${mediaFile.file_path}`, fsError);
        }
        // Continue even if file deletion fails - the database entry is removed
      }
      
      return { success: true, id };
    }
    
    return { success: false, id };
  } catch (error) {
    console.error(`Error deleting media file with ID ${id}:`, error);
    throw error;
  }
};

// Update a media file's note association
export const updateMediaFileNote = (id: number, noteId: number | null): MediaFile => {
  const query = 'UPDATE media_files SET note_id = ? WHERE id = ?';

  try {
    const result = dbRun(query, [noteId, id]);

    if (result.changes && result.changes > 0) {
      return getMediaFileById(id);
    }

    throw new Error(`Media file with ID ${id} not found or no changes made`);
  } catch (error) {
    console.error(`Error updating media file with ID ${id}:`, error);
    throw error;
  }
};

// Export the media API as a default object
export default {
  saveMediaFile,
  getMediaFileById,
  getMediaFilesByNoteId,
  getMediaFilesByBookId,
  getBookCover,
  saveBookCover,
  deleteMediaFile,
  updateMediaFileNote,
  getMediaStoragePath,
  filePathToMediaUrl
};